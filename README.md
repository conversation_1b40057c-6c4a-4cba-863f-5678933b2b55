# Escape Room Game System

## Running the System

1. Start the MQTT broker (192.168.178.34:1883)
2. Import the Node-RED flow from `node_red/flow.json`
3. Deploy the flow in Node-RED
4. Start Team A's ESP32 device
5. Start Team B's interface
6. Open the admin interface to monitor and control the game

## Game Flow

1. Both teams need to be ready to start
2. Team A generates and communicates a code to Team B
3. Team B enters the code to unlock the weight challenge
4. Team A must achieve the target weight using the pressure sensor
5. Game completes when both teams successfully complete their tasks

## Components

- `node_red/` - Contains the Node-RED flow for game control and admin interface
- `team_a/` - Team A's ESP32 code for code generation and weight sensor
- `team_b/` - Team B's interface for code entry and game progress
- `escape_room_documentation.md` - Detailed system documentation
