# utils/analytics.py
# Game analytics and performance tracking system

import time
import json
from utils.logger import log

class GameAnalytics:
    """Track game performance, errors, and statistics"""
    
    def __init__(self):
        self.start_time = time.time()
        self.session_data = {
            'games_played': 0,
            'games_completed': 0,
            'games_failed': 0,
            'stage1_successes': 0,
            'stage2_successes': 0,
            'total_attempts': 0,
            'average_completion_time': 0,
            'errors': [],
            'mqtt_disconnections': 0,
            'hardware_errors': 0,
            'button_presses': 0,
            'display_updates': 0,
            'sensor_readings': 0
        }
        self.current_game = None
        self.performance_metrics = {
            'memory_usage': [],
            'loop_times': [],
            'mqtt_latency': []
        }
    
    def start_game(self, game_id=None):
        """Start tracking a new game session"""
        if game_id is None:
            game_id = f"game_{int(time.time())}"
            
        self.current_game = {
            'id': game_id,
            'start_time': time.time(),
            'end_time': None,
            'stage1_attempts': 0,
            'stage2_attempts': 0,
            'stage1_completed': False,
            'stage2_completed': False,
            'completed': False,
            'failed': False,
            'errors': [],
            'events': []
        }
        
        self.session_data['games_played'] += 1
        self.log_event('game_started', {'game_id': game_id})
        
        log(f"Analytics: Started tracking game {game_id}", "DEBUG", "Analytics")
    
    def end_game(self, completed=False, failed=False):
        """End current game tracking"""
        if not self.current_game:
            return
            
        self.current_game['end_time'] = time.time()
        self.current_game['completed'] = completed
        self.current_game['failed'] = failed
        
        # Update session statistics
        if completed:
            self.session_data['games_completed'] += 1
            completion_time = self.current_game['end_time'] - self.current_game['start_time']
            self._update_average_completion_time(completion_time)
            
        if failed:
            self.session_data['games_failed'] += 1
        
        if self.current_game['stage1_completed']:
            self.session_data['stage1_successes'] += 1
            
        if self.current_game['stage2_completed']:
            self.session_data['stage2_successes'] += 1
        
        self.session_data['total_attempts'] += (
            self.current_game['stage1_attempts'] + 
            self.current_game['stage2_attempts']
        )
        
        self.log_event('game_ended', {
            'completed': completed,
            'failed': failed,
            'duration': self.current_game['end_time'] - self.current_game['start_time']
        })
        
        log(f"Analytics: Game ended - Completed: {completed}, Failed: {failed}", "INFO", "Analytics")
        self.current_game = None
    
    def log_stage_attempt(self, stage, attempt_number, success=False):
        """Log stage attempt"""
        if not self.current_game:
            return
            
        if stage == 1:
            self.current_game['stage1_attempts'] = attempt_number
            if success:
                self.current_game['stage1_completed'] = True
        elif stage == 2:
            self.current_game['stage2_attempts'] = attempt_number
            if success:
                self.current_game['stage2_completed'] = True
        
        self.log_event('stage_attempt', {
            'stage': stage,
            'attempt': attempt_number,
            'success': success
        })
    
    def log_error(self, error_type, error_message, component=None):
        """Log error with context"""
        error_data = {
            'timestamp': time.time(),
            'type': error_type,
            'message': error_message,
            'component': component
        }
        
        self.session_data['errors'].append(error_data)
        
        if self.current_game:
            self.current_game['errors'].append(error_data)
        
        # Update error counters
        if error_type == 'mqtt_disconnection':
            self.session_data['mqtt_disconnections'] += 1
        elif error_type == 'hardware_error':
            self.session_data['hardware_errors'] += 1
        
        self.log_event('error', error_data)
    
    def log_event(self, event_type, data=None):
        """Log general game event"""
        event = {
            'timestamp': time.time(),
            'type': event_type,
            'data': data or {}
        }
        
        if self.current_game:
            self.current_game['events'].append(event)
    
    def track_button_press(self, button_name):
        """Track button interaction"""
        self.session_data['button_presses'] += 1
        self.log_event('button_press', {'button': button_name})
    
    def track_display_update(self, line_number=None):
        """Track display updates"""
        self.session_data['display_updates'] += 1
        self.log_event('display_update', {'line': line_number})
    
    def track_sensor_reading(self, sensor_type, value):
        """Track sensor readings"""
        self.session_data['sensor_readings'] += 1
        self.log_event('sensor_reading', {
            'sensor': sensor_type,
            'value': value
        })
    
    def track_performance(self, metric_type, value):
        """Track performance metrics"""
        if metric_type in self.performance_metrics:
            self.performance_metrics[metric_type].append({
                'timestamp': time.time(),
                'value': value
            })
            
            # Keep only last 100 measurements
            if len(self.performance_metrics[metric_type]) > 100:
                self.performance_metrics[metric_type].pop(0)
    
    def get_session_summary(self):
        """Get summary of current session"""
        uptime = time.time() - self.start_time
        
        summary = {
            'session_uptime': uptime,
            'games_played': self.session_data['games_played'],
            'games_completed': self.session_data['games_completed'],
            'games_failed': self.session_data['games_failed'],
            'success_rate': self._calculate_success_rate(),
            'stage1_success_rate': self._calculate_stage_success_rate(1),
            'stage2_success_rate': self._calculate_stage_success_rate(2),
            'average_completion_time': self.session_data['average_completion_time'],
            'total_errors': len(self.session_data['errors']),
            'mqtt_disconnections': self.session_data['mqtt_disconnections'],
            'hardware_errors': self.session_data['hardware_errors'],
            'user_interactions': self.session_data['button_presses'],
            'display_updates': self.session_data['display_updates'],
            'sensor_readings': self.session_data['sensor_readings']
        }
        
        return summary
    
    def get_performance_stats(self):
        """Get performance statistics"""
        stats = {}
        
        for metric, measurements in self.performance_metrics.items():
            if measurements:
                values = [m['value'] for m in measurements]
                stats[metric] = {
                    'count': len(values),
                    'average': sum(values) / len(values),
                    'min': min(values),
                    'max': max(values),
                    'latest': values[-1] if values else 0
                }
            else:
                stats[metric] = {'count': 0, 'average': 0, 'min': 0, 'max': 0, 'latest': 0}
        
        return stats
    
    def get_recent_errors(self, count=10):
        """Get recent errors"""
        return self.session_data['errors'][-count:]
    
    def export_data(self):
        """Export all analytics data"""
        return {
            'session_data': self.session_data,
            'current_game': self.current_game,
            'performance_metrics': self.performance_metrics,
            'summary': self.get_session_summary(),
            'performance_stats': self.get_performance_stats()
        }
    
    def _calculate_success_rate(self):
        """Calculate overall success rate"""
        if self.session_data['games_played'] == 0:
            return 0
        return (self.session_data['games_completed'] / self.session_data['games_played']) * 100
    
    def _calculate_stage_success_rate(self, stage):
        """Calculate stage success rate"""
        if self.session_data['games_played'] == 0:
            return 0
            
        if stage == 1:
            return (self.session_data['stage1_successes'] / self.session_data['games_played']) * 100
        elif stage == 2:
            return (self.session_data['stage2_successes'] / self.session_data['games_played']) * 100
        
        return 0
    
    def _update_average_completion_time(self, new_time):
        """Update average completion time"""
        current_avg = self.session_data['average_completion_time']
        completed_games = self.session_data['games_completed']
        
        if completed_games == 1:
            self.session_data['average_completion_time'] = new_time
        else:
            # Running average calculation
            self.session_data['average_completion_time'] = (
                (current_avg * (completed_games - 1) + new_time) / completed_games
            )
    
    def reset_session(self):
        """Reset session data"""
        self.__init__()
        log("Analytics: Session data reset", "INFO", "Analytics")

# Global analytics instance
_analytics = GameAnalytics()

# Convenience functions
def get_analytics():
    """Get the global analytics instance"""
    return _analytics

def start_game_tracking(game_id=None):
    """Start tracking a new game"""
    _analytics.start_game(game_id)

def end_game_tracking(completed=False, failed=False):
    """End current game tracking"""
    _analytics.end_game(completed, failed)

def log_analytics_error(error_type, message, component=None):
    """Log error to analytics"""
    _analytics.log_error(error_type, message, component)

def track_user_action(action_type, data=None):
    """Track user action"""
    _analytics.log_event(action_type, data) 