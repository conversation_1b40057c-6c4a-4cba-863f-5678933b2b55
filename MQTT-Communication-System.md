# ESP32 Escape Room - MQTT Communication System Analysis

## Overview

The escape room system uses <PERSON><PERSON><PERSON> (Message Queuing Telemetry Transport) as the backbone for real-time communication between ESP32 devices and the Node-RED dashboard. This document provides a comprehensive analysis of the pub/sub messaging architecture, data flow patterns, and integration points.

## MQTT Broker Configuration

### Connection Details
- **Broker IP**: **************
- **Port**: 1883 (standard MQTT port)
- **Protocol**: MQTT v3.1.1
- **Security**: No authentication (local network)
- **QoS Level**: 0 (fire-and-forget for real-time performance)

### Client Identification
- **Team A ESP32**: `team_a_` + unique device ID
- **Team B ESP32**: `team_b_` + unique device ID
- **Node-RED**: Acts as MQTT client for dashboard integration

## MQTT Topic Structure

### Core Game Control Topics

```
escape_room/
├── game_control              # Game start/stop/reset commands
├── team_a/
│   ├── ready                 # Team A ready status
│   ├── status                # Periodic status updates
│   └── code_generated        # Stage 1 code generation
├── team_b/
│   ├── ready                 # Team B ready status
│   ├── status                # Periodic status updates
│   └── code_attempt          # Stage 1 code entry attempts
├── stage1/
│   └── code_generated        # Code generation notifications
├── stage2/
│   └── target_weight         # Weight challenge parameters
├── game_completed            # Game success notifications
├── game_failed               # Game failure notifications
└── admin                     # Administrative dashboard data
```

## Message Payload Structures

### Game Control Messages

**Topic**: `escape_room/game_control`

```json
// Game Start
{
  "action": "start_game",
  "team": "A",
  "game_start_time": 1703123456789,
  "timestamp": 1703123456789
}

// Teams Ready
{
  "action": "teams_ready", 
  "team": "A",
  "timestamp": 1703123456789
}

// Game Reset
{
  "action": "reset_game",
  "timestamp": 1703123456789
}
```

### Team Status Messages

**Topic**: `escape_room/team_a/status` / `escape_room/team_b/status`

```json
{
  "team": "A",
  "game_state": "stage1",
  "ready": true,
  "team_b_ready": true,
  "current_code": 456,
  "target_weight": 5000,
  "current_weight": 4850,
  "stage1_completed": true,
  "stage2_completed": false,
  "timestamp": 1703123456789
}
```

### Ready State Messages

**Topic**: `escape_room/team_a/ready` / `escape_room/team_b/ready`

```json
{
  "team": "A",
  "ready": true,
  "timestamp": 1703123456789
}
```

### Stage 1 - Code Generation

**Topic**: `escape_room/stage1/code_generated`

```json
{
  "code": 456,
  "timestamp": 1703123456789
}
```

### Stage 1 - Code Attempts

**Topic**: `escape_room/team_b/code_attempt`

```json
{
  "code": 456,
  "attempt": 3,
  "attempts_remaining": 2,
  "timestamp": 1703123456789
}
```

### Stage 2 - Weight Challenge

**Topic**: `escape_room/stage2/target_weight`

```json
{
  "stage": 2,
  "target_weight": 5000,
  "timestamp": 1703123456789
}
```

### Game Completion

**Topic**: `escape_room/game_completed`

```json
{
  "game_completed": true,
  "total_time_ms": 245000,
  "formatted_time": "4:05",
  "timestamp": 1703123456789
}
```

## Communication Patterns

### 1. Game Initialization Sequence

```
1. Both ESP32s → escape_room/team_x/ready
2. Node-RED monitors ready states
3. When both ready → escape_room/game_control (teams_ready)
4. Auto-start timer triggers → escape_room/game_control (start_game)
```

### 2. Stage 1 Code Flow

```
1. Team A ESP32 → escape_room/stage1/code_generated
2. Team B attempts → escape_room/team_b/code_attempt
3. Team A validates and either:
   - Success → escape_room/stage2/target_weight
   - Failure → Continue attempts or escape_room/game_failed
```

### 3. Stage 2 Weight Flow

```
1. Team A ESP32 → escape_room/stage2/target_weight
2. Team B ESP32 receives target weight
3. Team A monitors sensor and publishes:
   - Success → escape_room/game_completed
   - Timeout → escape_room/game_failed
```

### 4. Status Broadcasting

```
Every 2 seconds:
- Team A ESP32 → escape_room/team_a/status
- Team B ESP32 → escape_room/team_b/status
- Node-RED aggregates for dashboard updates
```

## Node-RED Integration

### MQTT Input Nodes
- **Subscribe to all escape_room/* topics**
- **Parse JSON payloads** for dashboard processing
- **Route messages** to appropriate WebSocket clients
- **Store game state** in Node-RED context variables

### WebSocket Bridge
- **MQTT → WebSocket**: Game status updates to web interfaces
- **WebSocket → MQTT**: Admin commands (start/reset) to ESP32s
- **Message filtering**: Only relevant data sent to each team interface

### Data Persistence
Node-RED maintains game state in context variables:
- `team_a_status` - Latest Team A status
- `team_b_status` - Latest Team B status  
- `game_start_time` - Game start timestamp
- `current_code` - Active code for Stage 1
- `target_weight` - Weight target for Stage 2
- `communication_log` - 2-character message history

## Error Handling and Resilience

### Connection Failures
- **ESP32 MQTT reconnection**: Automatic retry with exponential backoff
- **Graceful degradation**: Local operation when MQTT unavailable
- **Test mode**: Simulated team interactions for development

### Message Reliability
- **QoS 0**: Fast delivery, no guarantees (suitable for real-time status)
- **Duplicate handling**: Timestamp-based deduplication
- **Timeout mechanisms**: Game progression timeouts prevent hanging

### Network Resilience
- **WiFi reconnection**: Automatic reconnection on network drops
- **MQTT keep-alive**: Heartbeat messages maintain connections
- **Status validation**: Cross-reference between teams for consistency

## Performance Characteristics

### Message Frequency
- **Status updates**: Every 2 seconds per ESP32
- **Game events**: Immediate (button presses, sensor readings)
- **WebSocket updates**: Real-time dashboard refresh

### Latency Requirements
- **Critical path**: Code attempt → validation < 100ms
- **Status updates**: < 500ms for dashboard responsiveness
- **Weight sensor**: < 50ms for accurate readings

### Scalability
- **Single game instance**: Current implementation
- **Multi-game potential**: Topic namespacing could support multiple rooms
- **Concurrent players**: Limited by MQTT broker capacity

## Security Considerations

### Current Implementation
- **No authentication**: Suitable for isolated local network
- **No encryption**: Plain text MQTT for simplicity
- **Network isolation**: Assumes trusted local environment

### Production Recommendations
- **TLS encryption**: MQTT over SSL/TLS for secure communication
- **Authentication**: Username/password or certificate-based auth
- **Access control**: Topic-based permissions for team isolation
- **Network segmentation**: VLAN isolation for game networks

This MQTT architecture provides the real-time, reliable communication foundation that enables the collaborative escape room experience while maintaining clear separation between physical hardware interactions and web-based team communication.
