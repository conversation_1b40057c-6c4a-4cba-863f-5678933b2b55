# boot.py
# This file is executed on every boot (including wake-boot from deepsleep)

import network
import time
from secrets import WIFI_SSID, WIFI_PASSWORD

def connect_wifi(ssid, password):
    """Connect to WiFi network"""
    wlan = network.WLAN(network.STA_IF)
    wlan.active(True)
    
    if not wlan.isconnected():
        print("Connecting to Wi-Fi...", end='')
        time.sleep(0.5)
        wlan.connect(ssid, password)
        retries = 0
        
        while not wlan.isconnected() and retries < 20:
            print(".", end='')
            time.sleep(1)
            retries += 1

    if wlan.isconnected():
        print("\n✅ Connected to Wi-Fi!")
        config = wlan.ifconfig()
        print(f"📱 IP Address: {config[0]}")
        print(f"🌐 Subnet Mask: {config[1]}")
        print(f"🚪 Gateway: {config[2]}")
        print(f"🔍 DNS: {config[3]}")
    else:
        print("\n❌ Failed to connect to Wi-Fi.")
        print("Please check your WiFi credentials in secrets.py")
    
    return wlan

# Connect to WiFi
print("🚀 ESP32 Escape Room System Starting...")
print("=" * 40)
wlan = connect_wifi(WIFI_SSID, WIFI_PASSWORD)

# Show system info
try:
    import machine
    print(f"🔧 Unique ID: {machine.unique_id()}")
    print(f"⚡ Frequency: {machine.freq()} Hz")
    print("=" * 40)
except:
    pass
