# utils/helpers.py
# Helper functions for the escape room project

def calculate_moving_average(readings, window_size=None):
    """Calculate moving average of readings"""
    if not readings:
        return 0
    
    if window_size is None:
        window_size = len(readings)
    
    # Use only the last window_size readings
    recent_readings = readings[-window_size:]
    return sum(recent_readings) / len(recent_readings)

def clamp(value, min_value, max_value):
    """Clamp value between min and max"""
    return max(min_value, min(value, max_value))

def map_range(value, in_min, in_max, out_min, out_max):
    """Map value from one range to another"""
    return (value - in_min) * (out_max - out_min) / (in_max - in_min) + out_min

def format_time(seconds):
    """Format seconds into MM:SS format"""
    minutes = int(seconds // 60)
    seconds = int(seconds % 60)
    return f"{minutes:02d}:{seconds:02d}"

def debounce_time_passed(last_time, debounce_ms):
    """Check if enough time has passed for debouncing"""
    import time
    current_time = time.ticks_ms()
    return time.ticks_diff(current_time, last_time) > debounce_ms

def generate_random_code():
    """Generate a random 3-digit code"""
    import random
    return random.randint(100, 999)

def generate_random_weight(min_weight=1000, max_weight=10000):
    """Generate a random target weight in whole kg increments"""
    import random
    # Generate weight in 1kg increments (1000g)
    weight = random.randint(min_weight // 1000, max_weight // 1000) * 1000
    return weight

def validate_code(code):
    """Validate that code is a 3-digit number"""
    return 100 <= code <= 999

def validate_weight(weight):
    """Validate that weight is reasonable"""
    return 0 <= weight <= 10000  # 10kg max
