# utils/display_manager.py
# Display manager for LCD optimization and caching

import time
from utils.logger import log

class DisplayManager:
    """Manages LCD display updates with caching and optimization"""
    
    def __init__(self, lcd_instance, cols=20, rows=4):
        self.lcd = lcd_instance
        self.cols = cols
        self.rows = rows
        self.screen_cache = {}
        self.last_update = {}
        self.dirty_lines = set()
        self.update_interval = 0.1  # Minimum 100ms between updates
        self.force_update = False
        
        # Initialize cache
        for row in range(rows):
            self.screen_cache[row] = ""
            self.last_update[row] = 0
    
    def clear_screen(self, force=False):
        """Clear the LCD screen and cache"""
        try:
            if force or self._needs_update():
                self.lcd.clear()
                # Clear cache
                for row in range(self.rows):
                    self.screen_cache[row] = ""
                    self.last_update[row] = time.time()
                self.dirty_lines.clear()
                log("LCD cleared", "DEBUG", "DisplayManager")
        except Exception as e:
            log(f"Error clearing LCD: {e}", "ERROR", "DisplayManager")
    
    def write_line(self, row, text, force=False):
        """Write text to a specific line with caching"""
        if row < 0 or row >= self.rows:
            log(f"Invalid row: {row}", "WARNING", "DisplayManager")
            return False
            
        # Truncate text to fit display
        text = str(text)[:self.cols]
        
        # Pad with spaces if shorter than display width
        text = text.ljust(self.cols)
        
        # Check if update is needed
        if not force and self.screen_cache.get(row) == text:
            return True  # No change needed
        
        # Check if enough time has passed for this line
        current_time = time.time()
        if not force and current_time - self.last_update.get(row, 0) < self.update_interval:
            # Mark as dirty for later update
            self.dirty_lines.add(row)
            return False
        
        try:
            # Update LCD
            self.lcd.move_to(0, row)
            self.lcd.putstr(text)
            
            # Update cache
            self.screen_cache[row] = text
            self.last_update[row] = current_time
            
            # Remove from dirty lines
            self.dirty_lines.discard(row)
            
            log(f"LCD line {row} updated: '{text.strip()}'", "DEBUG", "DisplayManager")
            return True
            
        except Exception as e:
            log(f"Error writing to LCD line {row}: {e}", "ERROR", "DisplayManager")
            return False
    
    def write_multi_line(self, lines, start_row=0, force=False):
        """Write multiple lines starting from start_row"""
        success = True
        for i, line in enumerate(lines):
            if start_row + i >= self.rows:
                break
            if not self.write_line(start_row + i, line, force):
                success = False
        return success
    
    def update_dirty_lines(self):
        """Update any lines marked as dirty"""
        if not self.dirty_lines:
            return True
            
        success = True
        for row in list(self.dirty_lines):  # Copy to avoid modification during iteration
            cached_text = self.screen_cache.get(row, "")
            if not self.write_line(row, cached_text, force=True):
                success = False
        
        return success
    
    def display_centered(self, row, text, force=False):
        """Display text centered on the specified row"""
        text = str(text)
        if len(text) > self.cols:
            text = text[:self.cols]
        
        padding = (self.cols - len(text)) // 2
        centered_text = " " * padding + text
        
        return self.write_line(row, centered_text, force)
    
    def display_right_aligned(self, row, text, force=False):
        """Display text right-aligned on the specified row"""
        text = str(text)
        if len(text) > self.cols:
            text = text[:self.cols]
        
        right_aligned_text = text.rjust(self.cols)
        
        return self.write_line(row, right_aligned_text, force)
    
    def display_status_line(self, status_text, force=False):
        """Display status on the last line"""
        return self.write_line(self.rows - 1, status_text, force)
    
    def display_error(self, error_text, duration=3):
        """Display error message temporarily"""
        old_line = self.screen_cache.get(self.rows - 1, "")
        
        # Show error
        self.display_status_line(f"ERROR: {error_text}", force=True)
        
        # Wait
        time.sleep(duration)
        
        # Restore old line
        self.write_line(self.rows - 1, old_line, force=True)
    
    def display_countdown(self, row, seconds, prefix="", force=False):
        """Display a countdown timer"""
        countdown_text = f"{prefix}{seconds:02d}s"
        return self.write_line(row, countdown_text, force)
    
    def get_screen_state(self):
        """Get current screen state for debugging"""
        return {
            'cache': self.screen_cache.copy(),
            'dirty_lines': list(self.dirty_lines),
            'last_update': self.last_update.copy()
        }
    
    def force_refresh(self):
        """Force refresh of entire screen"""
        self.clear_screen(force=True)
        for row in range(self.rows):
            cached_text = self.screen_cache.get(row, "")
            if cached_text:
                self.write_line(row, cached_text, force=True)
    
    def set_update_interval(self, interval):
        """Set minimum update interval"""
        self.update_interval = max(0.05, interval)  # Minimum 50ms
    
    def _needs_update(self):
        """Check if screen needs updating based on timing"""
        current_time = time.time()
        
        # Check if any line hasn't been updated in a while
        for row in range(self.rows):
            if current_time - self.last_update.get(row, 0) > 5:  # 5 seconds
                return True
        
        return False
    
    def maintenance_update(self):
        """Perform maintenance updates (call periodically)"""
        # Update dirty lines
        self.update_dirty_lines()
        
        # Check if screen needs refresh
        if self._needs_update():
            self.force_refresh()

# Convenience functions for backward compatibility
def create_display_manager(lcd_instance, cols=20, rows=4):
    """Create a new display manager instance"""
    return DisplayManager(lcd_instance, cols, rows) 