# config/hardware_config.py
# Hardware configuration for ESP32 Nano Escape Room Project

# Team A Hardware Configuration
TEAM_A_PINS = {
    'START_BUTTON': 2,      # GPIO2 - Multi-function button
    'STATUS_LED': 4,        # GPIO4
    'BUZZER': 5,            # GPIO5
    'PRESSURE_SENSOR': 3,   # GPIO3 (ADC) - Changed from GPIO1 (reserved)
    'LCD_SDA': 11,          # GPIO11
    'LCD_SCL': 12,          # GPIO12
}

# Team B Hardware Configuration
TEAM_B_PINS = {
    'CONFIRM_BUTTON': 13,           # GPIO13 - Multi-function button
    'STATUS_LED': 14,               # GPIO14
    'BUZZER': 7,                    # GPIO7
    'ROTARY_SWITCH_HUNDREDS': 6,    # GPIO6 (CLK)
    'ROTARY_SWITCH_TENS': 8,        # GPIO8 (CLK)
    'ROTARY_SWITCH_ONES': 9,        # GPIO9 (CLK)
    'LCD_SDA': 47,                  # GPIO47
    'LCD_SCL': 38,                  # GPIO38
}

# Rotary Switch Configuration
ROTARY_SWITCH_CONFIG = {
    'positions': 10,
    'debounce_ms': 5,
    'dt_pin_mapping': {
        6: 17,   # Hundreds: CLK=GPIO6, DT=GPIO17
        8: 18,   # Tens: CLK=GPIO8, DT=GPIO18
        9: 21,   # Ones: CLK=GPIO9, DT=GPIO21
    }
}

# LCD Configuration
LCD_CONFIG_TEAM_A = {
    'address': 0x27,
    'sda_pin': 11,
    'scl_pin': 12,
    'width': 20,    # Updated for 20x4 LCD
    'height': 4,    # Updated for 20x4 LCD
    'freq': 100000,
}

LCD_CONFIG_TEAM_B = {
    'address': 0x27,
    'sda_pin': 47,
    'scl_pin': 38,
    'width': 20,    # Updated for 20x4 LCD  
    'height': 4,    # Updated for 20x4 LCD
    'freq': 100000,
}

# Pressure Sensor Settings
PRESSURE_SENSOR_CONFIG = {
    'calibration_samples': 10,
    'scale_factor': 3030.3,
    'zero_offset': 0.0,
    'moving_average_window': 5,
    'change_threshold': 50,
    'max_reading': 12000,
    'adc_attenuation': 3,
}

# Button Settings
BUTTON_CONFIG = {
    'debounce_ms': 500,  # Standardized to 500ms for reliable operation
    'long_press_ms': 2000,
    'pull_up': True,
}

# LED Patterns
LED_PATTERNS = {
    'off': {'state': False},
    'on': {'state': True},
    'slow_blink': {'interval_ms': 1000},
    'fast_blink': {'interval_ms': 200},
    'pulse': {'interval_ms': 500},
    'message_notification': {'interval_ms': 150},
}

# Buzzer Sounds
BUZZER_SOUNDS = {
    'beep': {'frequency': 800, 'duration_ms': 200},
    'success': {'pattern': [(600, 150), (800, 150), (1000, 300)]},
    'error': {'pattern': [(300, 100)] * 3},
    'notification': {'pattern': [(1200, 150), (800, 150)]},
    'completion': {'pattern': [(523, 200), (659, 200), (784, 200), (1047, 400)]},
    'message_received': {'pattern': [(800, 100), (1000, 100), (1200, 200)]},
}

# MQTT Topics
MQTT_TOPICS = {
    'game_control': 'escape_room/game_control',
    'team_a_ready': 'escape_room/team_a/ready',
    'team_b_ready': 'escape_room/team_b/ready',
    'team_a_status': 'escape_room/team_a/status',
    'team_b_status': 'escape_room/team_b/status',
    'stage1_code': 'escape_room/stage1/code_generated',
    'code_attempt': 'escape_room/team_b/code_attempt',
    'stage2_weight': 'escape_room/stage2/target_weight',
    'game_completed': 'escape_room/game_completed',
    'admin_dashboard': 'escape_room/admin',
    'team_communication': 'escape_room/communication',
}

# Game Settings
GAME_CONFIG = {
    'max_attempts': 5,
    'weight_tolerance': 50,  # grams
    'min_target_weight': 1000,  # grams (1kg)
    'max_target_weight': 10000,  # grams (10kg)
    'status_update_interval': 2,  # seconds
    'timeout_minutes': 30,  # game timeout
}
