# ESP32 MicroPython Escape Room - Game Mechanics and Architecture

## Executive Summary

The ESP32 Escape Room is a collaborative hardware-based puzzle game where two teams in separate physical locations must work together to escape. Each team has an ESP32 microcontroller with custom sensors and displays, and they communicate through a restricted web interface that only allows 2-character messages. This communication limitation creates the core challenge that makes the escape room experience engaging and unique.

## Table of Contents
1. [System Overview](#system-overview)
2. [Game Flow and Logic](#game-flow-and-logic)
3. [Player Interactions](#player-interactions)
4. [Win/Lose Conditions](#winlose-conditions)
5. [Puzzle Mechanics](#puzzle-mechanics)
6. [Hardware Components](#hardware-components)
7. [Software Architecture](#software-architecture)

## System Overview

### Core Concept
The ESP32 Escape Room creates an authentic escape room experience through:
- **Physical Separation**: Two teams in different rooms with no direct communication
- **Hardware Interaction**: Real sensors, displays, and physical controls
- **Communication Puzzle**: Web interface limited to 2-character messages
- **Collaborative Requirement**: Neither team can succeed alone
- **Time Pressure**: Limited attempts and real-time challenges

### System Architecture

The system consists of two ESP32 microcontrollers, a Node-RED server, and web interfaces:

```
┌─────────────┐         MQTT          ┌─────────────┐
│   Team A    │◄─────────────────────►│   Team B    │
│    ESP32    │                       │    ESP32    │
└──────┬──────┘                       └──────┬──────┘
       │                                     │
       │              Node-RED               │
       └──────────►┌───────────┐◄───────────┘
                   │   MQTT    │
                   │  Broker   │
                   └─────┬─────┘
                         │
                   ┌─────▼─────┐
                   │    Web    │
                   │Dashboard  │
                   └───────────┘
```

## Game Flow and Logic

### 1. Initialization Phase

```mermaid
stateDiagram-v2
    [*] --> Boot: Power On
    Boot --> WiFiConnect: Run boot.py
    WiFiConnect --> InitHardware: Connected
    InitHardware --> WaitingForReady: Hardware Ready
    WaitingForReady --> BothReady: Both Teams Press Ready
    BothReady --> Countdown: 3 Second Timer
    Countdown --> Stage1: Auto Start
```

**Detailed Process:**
1. **Boot Sequence** (boot.py):
   - Connect to WiFi with retry logic (up to 20 attempts)
   - Display connection status and IP address
   - Show system information (unique ID, frequency)

2. **Hardware Initialization** (main.py):
   - Team A: Initialize LCD, pressure sensor, button, LED, buzzer
   - Team B: Initialize LCD, rotary switches, confirm button, LED, buzzer
   - Establish MQTT connection to broker

3. **Ready Synchronization**:
   - Each team presses their ready button independently
   - System publishes ready status via MQTT
   - When both teams ready: 3-second countdown begins
   - Game auto-starts after countdown

### 2. Stage 1: Code Communication Challenge

```python
# Team A generates code when game starts
def start_game(self):
    self.current_code = random.randint(100, 999)
    self.game_state = "stage1"
    # Publish code via MQTT
    self.mqtt_client.publish(b"escape_room/stage1/code_generated", 
                           json.dumps({"code": self.current_code}))
```

**Team A Process:**
1. Generates random 3-digit code (100-999)
2. Displays code on LCD: "Code: 456"
3. Opens web interface at `/team-a`
4. Sends code using 2-character messages:
   - Strategy 1: "45" then "6"
   - Strategy 2: "4-" then "56"
   - Strategy 3: "H4" (hundreds), "T5" (tens), "O6" (ones)

**Team B Process:**
1. Opens web interface at `/team-b`
2. Receives 2-character messages
3. Decodes the complete 3-digit code
4. Uses rotary switches to input:
   - Hundreds dial: Rotate to position 4
   - Tens dial: Rotate to position 5
   - Ones dial: Rotate to position 6
5. Presses confirm button to submit attempt
6. LCD shows: "Attempts left: 4"

**Code Verification Logic:**
```python
def handle_code_attempt(self, data):
    attempted_code = data.get("code")
    attempts_remaining = data.get("attempts_remaining")
    
    if attempted_code == self.current_code:
        # Success!
        self.stage1_completed = True
        self.game_state = "stage2"
        self.generate_target_weight()
        self.play_sound("success")
    elif attempts_remaining <= 0:
        # Failure - no attempts left
        self.game_state = "failed"
        self.play_sound("error")
```

### 3. Stage 2: Weight Challenge

```python
# Team A generates target weight after Stage 1 success
def generate_target_weight(self):
    # Random weight: 1kg to 10kg in 250g increments
    raw_weight = random.randint(4, 40)
    self.target_weight = raw_weight * 250  # 1000g to 10000g
    
    # Publish to Team B
    self.mqtt_client.publish(b"escape_room/stage2/target_weight",
                           json.dumps({"target_weight": self.target_weight}))
```

**Team B Actions:**
1. LCD displays: "Need: 5kg"
2. Opens web interface messaging
3. Communicates weight to Team A:
   - Option 1: "5K" (5 kilograms)
   - Option 2: "50", "00" (5000 grams)
   - Option 3: "5#" (5 with unit marker)

**Team A Actions:**
1. Receives weight target via web messages
2. LCD shows real-time weight: "Current: 3kg"
3. Team physically applies weight to pressure sensor
4. System monitors for stable reading:
   ```python
   def check_pressure_sensor(self):
       current_weight = self.pressure_sensor.read_weight()
       tolerance = 500  # ±500g
       
       if abs(current_weight - self.target_weight) <= tolerance:
           # Weight in range - start timer
           if hold_duration >= 1.0:  # Hold for 1 second
               self.handle_stage2_completed()
   ```

**Pressure Sensor Mechanics:**
- Voltage range: 3.3V (no pressure) → 1.0V (max pressure)
- Weight calculation: `weight = (3.3 - voltage) * (10000 / 2.3)`
- Moving average filter: 10 samples for stability
- Success tolerance: ±500g from target
- Hold duration: 1 second continuous

### 4. Game Completion

When Stage 2 is completed:

```python
def handle_stage2_completed(self):
    # Calculate total time
    elapsed_ms = int(time.time() * 1000) - int(self.game_start_time)
    minutes = elapsed_ms // 60000
    seconds = (elapsed_ms % 60000) // 1000
    self.completion_time = f"{minutes}:{seconds:02d}"
    
    # Update displays
    self.game_state = "completed"
    self.lcd.print("*** GAME WON! ***")
    self.lcd.print(f"Time: {self.completion_time}")
    
    # Celebration
    self.play_sound("completion")
    self.led_pattern = "pulse"
```

**Victory Indicators:**
- LCD: "*** ESCAPED! ***" with completion time
- LED: Pulsing pattern
- Buzzer: Victory melody (C-E-G-C notes)
- Web dashboard: Confetti animation

## Player Interactions

### Team A Controls
- **Start Button (GPIO2)**: Ready signal and game start
- **LCD Display (20x4)**: Shows generated code, current weight, game status
- **Pressure Sensor (GPIO3)**: Detects applied weight (0-10kg range)
- **Web Interface (/team-a)**: Send 2-character messages to Team B

### Team B Controls
- **Rotary Switches (3x)**:
  - Hundreds (GPIO6/17): Select 0-9 for hundreds digit
  - Tens (GPIO8/18): Select 0-9 for tens digit
  - Ones (GPIO9/21): Select 0-9 for ones digit
- **Confirm Button (GPIO13)**: Submit code attempt
- **LCD Display (20x4)**: Shows input code, attempts left, target weight
- **Web Interface (/team-b)**: Send 2-character messages to Team A

## Win/Lose Conditions

### Victory Path
✅ **Stage 1**: Team B correctly enters code within 5 attempts
✅ **Stage 2**: Team A applies and holds target weight (±500g) for 1 second
✅ **Game Won**: Both stages completed, total time displayed

### Failure Conditions
❌ **Code Failure**: Team B uses all 5 attempts without correct code
❌ **Communication Breakdown**: Teams unable to decode messages
❌ **Hardware Malfunction**: Sensor or display failure

### Game State Machine
```
[waiting] --ready--> [ready] --start--> [stage1] --success--> [stage2] --success--> [completed]
                                            │                      │
                                            └--fail--> [failed] <--┘
```

## Puzzle Mechanics

### The 2-Character Communication Challenge

The core puzzle mechanic is the severely restricted communication channel:

**Web Interface Design:**
```html
<!-- Team communication input -->
<input type="text" id="char1" maxlength="1" placeholder="_">
<input type="text" id="char2" maxlength="1" placeholder="_">
<button onclick="sendMessage()">Send</button>
```

**Communication Strategies:**

1. **Sequential Transmission**
   - Code 456: Send "45", then "6_"
   - Code 789: Send "78", then "9_"

2. **Position Encoding**
   - "H4" = Hundreds digit is 4
   - "T5" = Tens digit is 5
   - "O6" = Ones digit is 6

3. **Delimiter Usage**
   - "4-" followed by "56"
   - "#7" followed by "89"

4. **Weight Communication**
   - "5K" = 5 kilograms
   - "25" + "00" = 2500 grams
   - "W5" = Weight 5kg

### Puzzle Difficulty Factors

1. **Message Ambiguity**
   - Is "45" the first two digits or a complete number?
   - Does "5K" mean 5 kilograms or something else?

2. **Time Pressure**
   - Only 5 attempts for code entry
   - Weight must be held steady for full second
   - Teams naturally feel urgency

3. **Coordination Challenge**
   - Teams must establish communication protocol
   - No pre-agreed system allowed
   - Must adapt strategy based on partner's responses

4. **Physical Precision**
   - Weight application requires careful control
   - ±500g tolerance demands accuracy
   - Sensor readings fluctuate with movement

## Hardware Components

### Team A Hardware Configuration
```
ESP32-S3 (ESP32 Nano)
├── LCD Display (I2C)
│   ├── Model: 20x4 character LCD with I2C backpack
│   ├── Address: 0x27
│   ├── Pins: SDA=GPIO11, SCL=GPIO12
│   └── Library: Custom LCD driver (lcd.py)
├── Pressure Sensor (ADC)
│   ├── Type: Analog pressure/force sensor
│   ├── Pin: GPIO3 (ADC input)
│   ├── Range: 0-10kg
│   └── Calibration: Auto-zero on startup
├── Start Button
│   ├── Pin: GPIO2
│   ├── Type: Momentary push button
│   └── Pull-up: Internal enabled
├── Status LED
│   ├── Pin: GPIO4
│   ├── Type: Green LED
│   └── Patterns: Off, On, Slow blink, Fast blink, Pulse
└── Buzzer
    ├── Pin: GPIO5 (PWM)
    ├── Type: Piezo buzzer
    └── Sounds: Beep, Success, Error, Completion melody
```

### Team B Hardware Configuration
```
ESP32-S3 (ESP32 Nano)
├── LCD Display (I2C)
│   ├── Model: 20x4 character LCD
│   ├── Address: 0x27
│   └── Pins: SDA=GPIO47, SCL=GPIO38
├── Rotary Switches (3x)
│   ├── Hundreds Switch
│   │   ├── CLK: GPIO6
│   │   ├── DT: GPIO17
│   │   └── Positions: 0-9
│   ├── Tens Switch
│   │   ├── CLK: GPIO8
│   │   ├── DT: GPIO18
│   │   └── Positions: 0-9
│   └── Ones Switch
│       ├── CLK: GPIO9
│       ├── DT: GPIO21
│       └── Positions: 0-9
├── Confirm Button
│   ├── Pin: GPIO13
│   └── Function: Submit code attempt
├── Status LED
│   └── Pin: GPIO14
└── Buzzer
    └── Pin: GPIO7 (PWM)
```

## Software Architecture

### File Structure
```
EscapeRoom/
├── team_a/
│   ├── boot.py                 # WiFi connection on startup
│   ├── main.py                 # Team A game logic
│   ├── secrets.py              # WiFi credentials
│   ├── hardware/
│   │   ├── lcd.py              # I2C LCD driver
│   │   └── pressure_sensor.py  # ADC weight sensor
│   ├── config/
│   │   └── hardware_config.py  # Pin definitions
│   ├── utils/
│   │   ├── logger.py           # Logging system
│   │   ├── network_manager.py  # Network utilities
│   │   └── display_manager.py  # Display helpers
│   └── umqtt/
│       └── simple.py           # MQTT client
├── team_b/
│   ├── [similar structure]
│   └── hardware/
│       └── switches.py         # Rotary switch driver
└── node_red/
    ├── flow.json               # Node-RED configuration
    ├── admin.html              # Admin dashboard
    ├── team-a.html             # Team A web interface
    └── team-b.html             # Team B web interface
```

### Core Classes

**EscapeRoomTeamA Class:**
```python
class EscapeRoomTeamA:
    def __init__(self):
        self.init_hardware()      # Initialize all hardware
        self.connect_mqtt()       # Connect to MQTT broker
        self.game_state = "waiting"
        self.current_code = None
        self.target_weight = None
        
    def run(self):
        """Main game loop at 20Hz"""
        while True:
            self.mqtt_client.check_msg()     # Check MQTT messages
            self.check_button()              # Check ready button
            self.check_pressure_sensor()     # Monitor weight
            self.update_led()                # Update LED pattern
            self.publish_status()            # Send status updates
            time.sleep_ms(50)                # 20Hz loop
```

**Key Components:**

1. **Hardware Abstraction Layer**
   - Modular classes for each hardware component
   - Configurable via `hardware_config.py`
   - Error handling and graceful degradation

2. **Network Layer**
   - WiFi connection with 20-attempt retry
   - MQTT pub/sub for real-time updates
   - WebSocket integration via Node-RED

3. **Game State Machine**
   - Clear state transitions
   - MQTT synchronization between devices
   - Persistent state through game phases

4. **User Interface**
   - LCD display management
   - LED patterns for visual feedback
   - Sound effects for game events
   - Web dashboard integration

### MQTT Topics Structure
```
escape_room/
├── game_control           # Game start/stop/reset commands
├── team_a/
│   ├── ready             # Team A ready status
│   └── status            # Periodic status updates
├── team_b/
│   ├── ready             # Team B ready status
│   ├── code_attempt      # Code submission attempts
│   └── status            # Periodic status updates
├── stage1/
│   └── code_generated    # Generated code broadcast
├── stage2/
│   └── target_weight     # Target weight broadcast
├── game_completed        # Victory notification
└── game_failed           # Failure notification
```

### Performance Optimizations

1. **Sensor Reading**
   - 10-sample moving average for stability
   - Debounced button inputs (500ms)
   - Efficient ADC reading at 20Hz

2. **Network Efficiency**
   - Status updates every 2 seconds
   - Critical events sent immediately
   - JSON payload optimization

3. **Display Updates**
   - LCD refresh only on state change
   - Cached display content
   - Minimal I2C transactions

### Error Handling Strategy

1. **Network Failures**
   - Automatic reconnection attempts
   - Local game mode fallback
   - Cached state preservation

2. **Hardware Failures**
   - Graceful degradation
   - Error logging and reporting
   - Alternative input methods

3. **Game State Recovery**
   - Persistent state tracking
   - MQTT message acknowledgment
   - Timeout handling

This architecture creates a robust, engaging escape room experience that combines physical hardware challenges with digital communication puzzles, suitable for team building, education, and entertainment applications.
