# main.py for Team A ESP32
# Escape Room Game - Team A (Code Generator & Pressure Sensor)

import time
import random
import machine
import network
from umqtt.simple import MQTTClient
import json

# Import custom modules
from hardware.pressure_sensor import PressureSensor
from utils.logger import log

# Configuration
CLIENT_ID = "team_a_" + str(machine.unique_id())
BROKER = "192.168.178.34"
PORT = 1883

# Team A Pin Configuration
TEAM_A_PINS = {
    'START_BUTTON': 2,
    'STATUS_LED': 4,
    'BUZZER': 5,
    'PRESSURE_SENSOR': 3,
    'LCD_SDA': 11,
    'LCD_SCL': 12,
}

class EscapeRoomTeamA:
    def __init__(self):
        """Initialize Team A escape room system"""
        log("Initializing Team A Escape Room System", "INFO")
        
        # Hardware initialization
        self.init_hardware()
        
        # Game state
        self.game_state = "waiting"  # waiting, ready, stage1, stage2, completed, failed
        self.current_code = None
        self.target_weight = None
        self.ready = False
        self.team_b_ready = False  # Track Team B's ready status
        self.stage1_completed = False
        self.stage2_completed = False
        self.game_start_time = 0
        self.stage1_complete_time = 0
        self.stage2_complete_time = 0
        
        # MQTT
        self.mqtt_client = None
        self.connect_mqtt()
        
        # Display
        self.init_display()
        
        # Auto-start timer
        self.auto_start_time = 0
        
        # Test mode handling (when MQTT is not available)
        self.test_mode_start_time = 0
        
        # Stage 2 timing
        self.weight_in_range_start = 0  # When weight first entered target range
        
        log("Team A system initialized successfully!", "INFO")

    def init_hardware(self):
        """Initialize all hardware components"""
        # Start button
        self.start_button = machine.Pin(TEAM_A_PINS['START_BUTTON'], machine.Pin.IN, machine.Pin.PULL_UP)
        self.last_button_state = 1
        self.last_button_time = 0
        
        # Status LED
        self.status_led = machine.Pin(TEAM_A_PINS['STATUS_LED'], machine.Pin.OUT)
        self.led_state = False
        self.led_last_toggle = 0
        self.led_pattern = "off"
        
        # Buzzer
        self.buzzer = machine.PWM(machine.Pin(TEAM_A_PINS['BUZZER']))
        self.buzzer.duty(0)
        
        # Pressure sensor
        self.pressure_sensor = PressureSensor(TEAM_A_PINS['PRESSURE_SENSOR'])
        self.pressure_sensor.calibrate_zero()
        
        log("Hardware initialized", "INFO")

    def init_display(self):
        """Initialize LCD display"""
        try:
            log("Starting LCD initialization...", "INFO")
            from machine import I2C
            from hardware.lcd import LCD
            
            log(f"Creating I2C with SDA={TEAM_A_PINS['LCD_SDA']}, SCL={TEAM_A_PINS['LCD_SCL']}", "DEBUG")
            i2c = I2C(0, sda=machine.Pin(TEAM_A_PINS['LCD_SDA']), 
                     scl=machine.Pin(TEAM_A_PINS['LCD_SCL']), freq=100000)
            
            # Scan for I2C devices
            devices = i2c.scan()
            log(f"I2C scan found devices at addresses: {[hex(d) for d in devices]}", "DEBUG")
            
            if 0x27 not in devices:
                log("LCD not found at address 0x27!", "ERROR")
                self.lcd = None
                return
            
            log("Creating LCD object...", "DEBUG")
            self.lcd = LCD(i2c, 0x27, 20, 4)  # 20x4 LCD
            
            log("Clearing display...", "DEBUG")
            self.lcd.clear()
            
            log("Writing initial text...", "DEBUG")
            self.lcd.print("=== ESCAPE ROOM ===")
            self.lcd.set_cursor(0, 1)
            self.lcd.print("Team A")
            self.lcd.set_cursor(0, 2)
            self.lcd.print("Press button when")
            self.lcd.set_cursor(0, 3)
            self.lcd.print("ready to start!")            
            
            log("LCD initialized successfully", "INFO")
        except Exception as e:
            import sys
            log(f"LCD initialization failed: {e}", "ERROR")
            log(f"Error details: {sys.exc_info()[0]}", "ERROR")
            self.lcd = None

    def connect_mqtt(self):
        """Connect to MQTT broker with error handling"""
        try:
            self.mqtt_client = MQTTClient(CLIENT_ID, BROKER, PORT)
            self.mqtt_client.set_callback(self.mqtt_callback)
            self.mqtt_client.connect()
            
            # Subscribe to relevant topics
            self.mqtt_client.subscribe(b"escape_room/game_control")
            self.mqtt_client.subscribe(b"escape_room/team_b/ready")
            self.mqtt_client.subscribe(b"escape_room/team_b/code_attempt")
            self.mqtt_client.subscribe(b"escape_room/stage2/target_weight")
            
            log("Connected to MQTT broker", "INFO")
            return True
        except Exception as e:
            log(f"MQTT connection failed: {e}", "ERROR")
            log("Game will work locally without MQTT", "WARNING")
            self.mqtt_client = None
            return False

    def mqtt_callback(self, topic, msg):
        """Handle incoming MQTT messages"""
        try:
            topic = topic.decode()
            message = msg.decode()
            log(f"MQTT received: {topic} -> {message}", "DEBUG")
            
            if topic == "escape_room/game_control":
                data = json.loads(message)
                if data.get("action") == "start_game":
                    self.start_game()
                elif data.get("action") == "reset_game":
                    self.reset_game()
            
            elif topic == "escape_room/team_b/ready":
                data = json.loads(message)
                if data.get("ready"):
                    self.team_b_ready = True
                    log("Team B is ready! Checking if both teams ready", "DEBUG")
                    self.check_both_teams_ready()
            
            elif topic == "escape_room/team_b/code_attempt":
                data = json.loads(message)
                self.handle_code_attempt(data)
            
            elif topic == "escape_room/stage2/target_weight":
                data = json.loads(message)
                self.target_weight = data.get("weight")
                self.update_display()
                
        except Exception as e:
            log(f"MQTT callback error: {e}", "ERROR")

    def check_button(self):
        """Check start button with debouncing"""
        current_time = time.time() * 1000  # Convert to milliseconds
        button_state = self.start_button.value()
        
        # Debug button state less frequently
        if hasattr(self, 'last_debug_button_time'):
            if (current_time - self.last_debug_button_time) > 5000:  # Every 5 seconds
                log(f"Button: {button_state}, state: {self.game_state}, ready: {self.ready}", "DEBUG")
                self.last_debug_button_time = current_time
        else:
            self.last_debug_button_time = current_time
        
        # Only check for button press (falling edge) with proper debouncing
        if (self.last_button_state == 1 and button_state == 0 and 
            (current_time - self.last_button_time) > 500):
            
            log(f"BUTTON PRESSED! state: {self.game_state}, ready: {self.ready}", "INFO")
            
            if self.game_state == "waiting" and not self.ready:
                log("Processing ready button press", "DEBUG")
                self.set_ready()
                self.play_sound("beep")
                self.last_button_time = current_time
                log("Button pressed - Team A ready", "INFO")
            else:
                log(f"Button ignored - state: {self.game_state}, already_ready: {self.ready}", "DEBUG")
        
        self.last_button_state = button_state

    def set_ready(self):
        """Set team A as ready"""
        log(f"set_ready called - current ready state: {self.ready}", "DEBUG")
        
        if self.ready:  # Prevent multiple ready calls
            log("Already ready - ignoring duplicate call", "DEBUG")
            return
            
        self.ready = True
        log("Team A marked as ready", "DEBUG")
        self.update_display()
        
        # Publish ready status
        if self.mqtt_client:
            ready_data = {
                "team": "A",
                "ready": True,
                "timestamp": time.time()
            }
            try:
                self.mqtt_client.publish(b"escape_room/team_a/ready", json.dumps(ready_data))
                log("Published Team A ready status via MQTT", "DEBUG")
            except Exception as e:
                log(f"Failed to publish ready status: {e}", "WARNING")
        else:
            log("No MQTT client - ready status not published", "DEBUG")
        
        # CRITICAL FIX: Check if both teams are ready immediately after Team A becomes ready
        log("Team A is now ready - checking if both teams are ready", "INFO")
        self.check_both_teams_ready()
        
        # For testing without MQTT - simulate both teams ready after 3 seconds
        if not self.mqtt_client:
            log("No MQTT - will auto-start game in 3 seconds for testing", "INFO")
            self.test_mode_start_time = time.time()
        
        log("Team A is ready!", "INFO")

    def check_both_teams_ready(self):
        """Check if both teams are ready to start"""
        log(f"check_both_teams_ready - Team A: {self.ready}, Team B: {self.team_b_ready}, state: {self.game_state}", "DEBUG")
        
        if self.ready and self.team_b_ready and self.game_state == "waiting":
            log("🎉 BOTH TEAMS ARE READY! Initiating game start sequence", "INFO")
            self.game_state = "ready"
            self.update_display()
            log("Both teams ready! Starting game in 3 seconds...", "INFO")
            
            # Publish teams ready status
            if self.mqtt_client:
                ready_data = {
                    "action": "teams_ready",
                    "team": "A",
                    "timestamp": int(time.time() * 1000)
                }
                log(f"Publishing teams ready message: {ready_data}", "DEBUG")
                self.mqtt_client.publish(b"escape_room/game_control", json.dumps(ready_data))
            
            # Auto-start the game after 3 seconds
            self.auto_start_time = time.time() + 3
            log(f"Auto-start timer set for: {self.auto_start_time}", "DEBUG")
        else:
            log(f"Not starting yet - Team A ready: {self.ready}, Team B ready: {self.team_b_ready}, state: {self.game_state}", "DEBUG")

    def start_game(self):
        """Start the game"""
        try:
            log(f"Team A start_game called, current state: {self.game_state}", "INFO")
            
            if self.game_state not in ["ready", "waiting"]:
                log(f"Cannot start game from state: {self.game_state}", "WARNING")
                return
            
            # Generate random 3-digit code
            self.current_code = random.randint(100, 999)
            self.game_state = "stage1"
            self.game_start_time = time.time() * 1000  # Convert to milliseconds
            
            # Update display with generated code
            self.update_display()
            
            # Play start sound
            self.play_sound("success")
            
            # Publish game start and code
            if self.mqtt_client:
                # Publish game start command
                start_data = {
                    "action": "start_game",
                    "team": "A",
                    "game_start_time": int(time.time() * 1000),  # Convert to milliseconds timestamp
                    "timestamp": int(time.time() * 1000)
                }
                log(f"Publishing game start message: {start_data}", "DEBUG")
                self.mqtt_client.publish(b"escape_room/game_control", json.dumps(start_data))
                
                # Publish code separately
                code_data = {
                    "code": self.current_code,
                    "timestamp": int(time.time() * 1000)
                }
                log(f"Publishing code message: {code_data}", "DEBUG")
                self.mqtt_client.publish(b"escape_room/stage1/code_generated", json.dumps(code_data))
            
            log(f"🚀 GAME STARTED! Generated code: {self.current_code}", "INFO")
            
        except Exception as e:
            log(f"Error in start_game: {e}", "ERROR")

    def handle_code_attempt(self, data):
        """Handle code attempt from Team B"""
        if self.game_state != "stage1" or self.stage1_completed:
            return
        
        attempted_code = data.get("code")
        attempt_number = data.get("attempt")
        
        log(f"Code attempt received: {attempted_code}, correct: {self.current_code}", "INFO")
        
        if attempted_code == self.current_code:
            # Correct code!
            self.stage1_completed = True
            self.stage1_complete_time = time.time()
            self.game_state = "stage2"
            
            # Generate target weight for stage 2 (1000g to 10000g = 1kg to 10kg)
            # Round to nearest 250g for easier targeting (1000, 1250, 1500, 1750, 2000, etc.)
            raw_weight = random.randint(4, 40)  # 4-40 * 250g = 1000g-10000g
            self.target_weight = raw_weight * 250
            
            # Store it in a backup variable too
            self._stage2_target = self.target_weight
            
            log(f"✅ STAGE 1 COMPLETED! Generated target weight: {self.target_weight}g", "INFO")
            log(f"Game state set to: {self.game_state}", "DEBUG")
            
            # Update display immediately
            self.update_display()
            
            # Publish stage 2 info
            if self.mqtt_client:
                stage2_data = {
                    "stage": 2,
                    "target_weight": self.target_weight,
                    "timestamp": time.time()
                }
                self.mqtt_client.publish(b"escape_room/stage2/target_weight", json.dumps(stage2_data))
            
            self.play_sound("completion")
        
        else:
            # Wrong code - check the attempts_remaining from Team B
            attempts_remaining = data.get("attempts_remaining", 0)
            log(f"❌ Wrong code! Team B has {attempts_remaining} attempts remaining", "INFO")
            
            # Only fail if Team B has NO attempts left (0)
            if attempts_remaining <= 0:
                # Game over - no attempts left
                self.game_state = "failed"
                self.update_display()
                self.play_sound("error")
                log("💀 GAME FAILED - Team B has no attempts remaining", "INFO")
                
                # Notify Team B that game failed
                if self.mqtt_client:
                    fail_data = {
                        "game_failed": True,
                        "reason": "no_attempts_remaining",
                        "timestamp": time.time()
                    }
                    self.mqtt_client.publish(b"escape_room/game_failed", json.dumps(fail_data))

    def check_pressure_sensor(self):
        """Check pressure sensor for stage 2 - with detailed debugging"""
        if self.game_state != "stage2":
            return
            
        # Use backup target weight to avoid None issues
        target_weight = self.target_weight or getattr(self, '_stage2_target', None)
        
        if not target_weight:
            log("check_pressure_sensor: No target weight set", "DEBUG")
            return
        
        current_weight = self.pressure_sensor.read_weight()
        current_time = time.time()
        
        # Debug every few readings to avoid spam
        if hasattr(self, 'last_pressure_debug'):
            if current_time - self.last_pressure_debug > 2:  # Every 2 seconds
                log(f"Pressure: current={current_weight}g, target={target_weight}g", "DEBUG")
                self.last_pressure_debug = current_time
        else:
            self.last_pressure_debug = current_time
        
        # Very generous tolerance: ±500g
        tolerance = 500
        weight_difference = abs(current_weight - target_weight)
        weight_in_range = weight_difference <= tolerance
        
        if weight_in_range:
            # Weight is in range!
            if self.weight_in_range_start == 0:
                # First time entering range
                self.weight_in_range_start = current_time
                log(f"✅ Weight ENTERED range! Starting 1-second timer...", "INFO")
            
            # Check how long we've been in range (1 second)
            hold_duration = current_time - self.weight_in_range_start
            
            if hold_duration >= 1.0:  # Changed from 2.0 to 1.0 seconds
                # Stage 2 completed!
                self.handle_stage2_completed()
        else:
            # Weight is out of range - reset timer
            if self.weight_in_range_start > 0:
                log(f"❌ Weight LEFT range (diff: {weight_difference}g > {tolerance}g) - timer reset", "INFO")
            self.weight_in_range_start = 0

    def handle_stage2_completed(self):
        """Handle stage 2 completion"""
        log("🎉 STAGE 2 COMPLETED! Weight held for 1 second!", "INFO")
        
        # Calculate total time
        elapsed_ms = int(time.time() * 1000) - int(self.game_start_time)
        minutes = elapsed_ms // 60000
        seconds = (elapsed_ms % 60000) // 1000
        
        # Store formatted time for display
        self.completion_time = f"{minutes}:{seconds:02d}"
        
        log(f"🏆 GAME COMPLETED! Total time: {self.completion_time}", "INFO")
        
        # Publish game completion
        if self.mqtt_client:
            completion_data = {
                "game_completed": True,
                "total_time_ms": elapsed_ms,
                "formatted_time": self.completion_time,
                "timestamp": int(time.time() * 1000)
            }
            self.mqtt_client.publish(b"escape_room/game_completed", json.dumps(completion_data))
        
        self.game_state = "completed"
        self.update_display()
        self.play_sound("success")

    def update_display(self):
        """Update LCD display based on game state - optimized for 20x4 LCD"""
        if not self.lcd:
            return
        
        log(f"Updating display for game_state: {self.game_state}", "DEBUG")
        
        self.lcd.clear()
        
        if self.game_state == "waiting":
            if self.ready:
                self.lcd.print("Team A: READY")
                self.lcd.set_cursor(0, 1)
                if self.team_b_ready:
                    self.lcd.print("Team B: READY")
                    self.lcd.set_cursor(0, 2)
                    self.lcd.print("Both teams ready!")
                    self.lcd.set_cursor(0, 3)
                    self.lcd.print("Starting soon...")
                else:
                    self.lcd.print("Waiting for Team B...")
                    self.lcd.set_cursor(0, 2)
                    self.lcd.print("")
                    self.lcd.set_cursor(0, 3)
                    self.lcd.print("Status: Connected")
            else:
                self.lcd.print("=== ESCAPE ROOM ===")
                self.lcd.set_cursor(0, 1)
                self.lcd.print("Team A")
                self.lcd.set_cursor(0, 2)
                self.lcd.print("Press button when")
                self.lcd.set_cursor(0, 3)
                self.lcd.print("ready to start!")
        
        elif self.game_state == "ready":
            self.lcd.print("Both teams ready!")
            self.lcd.set_cursor(0, 1)
            self.lcd.print("Game starting soon...")
            self.lcd.set_cursor(0, 2)
            self.lcd.print("")
            self.lcd.set_cursor(0, 3)
            self.lcd.print("Get ready to escape!")
        
        elif self.game_state == "stage1":
            self.lcd.print("=== STAGE 1 ===")
            self.lcd.set_cursor(0, 1)
            self.lcd.print(f"Code: {self.current_code}")
            self.lcd.set_cursor(0, 2)
            self.lcd.print("Tell Team B via")
            self.lcd.set_cursor(0, 3)
            self.lcd.print("communication!")
        
        elif self.game_state == "stage2":
            self.lcd.print("=== STAGE 2 ===")
            self.lcd.set_cursor(0, 1)
            self.lcd.print("Weight Challenge")
            self.lcd.set_cursor(0, 2)
            current_weight = self.pressure_sensor.read_weight()
            current_kg = current_weight // 1000  # Integer division for whole numbers
            self.lcd.print(f"Current: {current_kg}kg")
            self.lcd.set_cursor(0, 3)
            self.lcd.print("Get target from B!")
        
        elif self.game_state == "completed":
            self.lcd.print("*** GAME WON! ***")
            self.lcd.set_cursor(0, 1)
            self.lcd.print("Congratulations!")
            self.lcd.set_cursor(0, 2)
            if hasattr(self, 'completion_time'):
                self.lcd.print(f"Time: {self.completion_time}")
            else:
                self.lcd.print("Mission accomplished!")
            self.lcd.set_cursor(0, 3)
            self.lcd.print("You escaped!")
        
        elif self.game_state == "failed":
            self.lcd.print("*** GAME OVER ***")
            self.lcd.set_cursor(0, 1)
            self.lcd.print("Mission failed!")
            self.lcd.set_cursor(0, 2)
            self.lcd.print("Too many wrong")
            self.lcd.set_cursor(0, 3)
            self.lcd.print("attempts.")
        
        log("Display update completed", "DEBUG")

    def update_led(self):
        """Update LED pattern based on game state"""
        current_time = time.ticks_ms()
        
        # Update LED pattern based on game state
        if self.game_state == "waiting":
            if self.ready:
                self.led_pattern = "slow_blink"
            else:
                self.led_pattern = "off"
        elif self.game_state == "ready":
            self.led_pattern = "fast_blink"
        elif self.game_state in ["stage1", "stage2"]:
            self.led_pattern = "on"
        elif self.game_state == "completed":
            self.led_pattern = "pulse"
        elif self.game_state == "failed":
            self.led_pattern = "off"
        
        # Handle LED patterns
        if self.led_pattern == "off":
            self.status_led.value(0)
        elif self.led_pattern == "on":
            self.status_led.value(1)
        elif self.led_pattern == "slow_blink":
            if time.ticks_diff(current_time, self.led_last_toggle) > 1000:
                self.led_state = not self.led_state
                self.status_led.value(self.led_state)
                self.led_last_toggle = current_time
        elif self.led_pattern == "fast_blink":
            if time.ticks_diff(current_time, self.led_last_toggle) > 200:
                self.led_state = not self.led_state
                self.status_led.value(self.led_state)
                self.led_last_toggle = current_time
        elif self.led_pattern == "pulse":
            if time.ticks_diff(current_time, self.led_last_toggle) > 500:
                self.led_state = not self.led_state
                self.status_led.value(self.led_state)
                self.led_last_toggle = current_time

    def play_sound(self, sound_type):
        """Play buzzer sound"""
        try:
            if sound_type == "beep":
                self.buzzer.freq(800)
                self.buzzer.duty(512)
                time.sleep_ms(200)
                self.buzzer.duty(0)
            
            elif sound_type == "success":
                frequencies = [600, 800, 1000]
                for freq in frequencies:
                    self.buzzer.freq(freq)
                    self.buzzer.duty(512)
                    time.sleep_ms(150)
                    self.buzzer.duty(0)
                    time.sleep_ms(50)
            
            elif sound_type == "error":
                for _ in range(3):
                    self.buzzer.freq(300)
                    self.buzzer.duty(512)
                    time.sleep_ms(100)
                    self.buzzer.duty(0)
                    time.sleep_ms(100)
            
            elif sound_type == "completion":
                frequencies = [523, 659, 784, 1047]
                durations = [200, 200, 200, 400]
                for freq, duration in zip(frequencies, durations):
                    self.buzzer.freq(freq)
                    self.buzzer.duty(512)
                    time.sleep_ms(duration)
                    self.buzzer.duty(0)
                    time.sleep_ms(50)
                    
        except Exception as e:
            log(f"Sound error: {e}", "ERROR")

    def reset_game(self):
        """Reset game to initial state"""
        try:
            # Reset game state variables
            self.game_state = "waiting"
            self.current_code = None
            self.target_weight = None
            self.ready = False
            self.team_b_ready = False
            self.stage1_completed = False
            self.stage2_completed = False
            self.game_start_time = 0
            self.stage1_complete_time = 0
            self.stage2_complete_time = 0
            
            # Reset hardware
            if hasattr(self, 'hardware_manager'):
                self.hardware_manager.reset_hardware()
            
            # Reset MQTT connection
            if hasattr(self, 'network_manager'):
                self.network_manager.reset_connection()
            
            # Reset display
            if hasattr(self, 'lcd'):
                self.lcd.clear()
                self.lcd.print("=== ESCAPE ROOM ===")
                self.lcd.set_cursor(0, 1)
                self.lcd.print("Team A")
                self.lcd.set_cursor(0, 2)
                self.lcd.print("Press button when")
                self.lcd.set_cursor(0, 3)
                self.lcd.print("ready to start!")
            
            # Reset LED pattern
            self.led_pattern = "off"
            self.status_led.value(0)
            
            log("Game reset completed", "INFO")
        except Exception as e:
            log(f"Error during game reset: {e}", "ERROR")

    def run(self):
        """Main game loop"""
        log("Starting Team A game loop", "INFO")
        
        while True:
            try:
                # Check MQTT messages
                if self.mqtt_client:
                    self.mqtt_client.check_msg()
                
                # Check button
                self.check_button()
                
                # Auto-start game when both teams ready
                if (self.game_state == "ready" and self.auto_start_time > 0 and 
                    time.time() >= self.auto_start_time):
                    log("⏰ Auto-start timer triggered - starting game!", "INFO")
                    self.auto_start_time = 0  # Reset timer
                    self.start_game()
                
                # Test mode - auto start game if MQTT not available
                if (not self.mqtt_client and self.ready and 
                    self.game_state == "waiting" and self.test_mode_start_time > 0):
                    if time.time() - self.test_mode_start_time > 3:
                        log("Test mode: Starting game automatically", "INFO")
                        self.game_state = "ready"
                        self.update_display()
                        time.sleep(2)  # Show "ready" state briefly
                        self.start_game()
                
                # Check pressure sensor for stage 2
                if self.game_state == "stage2":
                    self.check_pressure_sensor()
                    
                    # Update display more frequently in stage 2 to show real-time weight
                    if hasattr(self, 'last_display_update'):
                        if time.time() - self.last_display_update > 1.0:  # Update every 1 second
                            self.update_display()
                            self.last_display_update = time.time()
                    else:
                        self.last_display_update = time.time()
                
                # Update LED
                self.update_led()
                
                # Publish current status periodically
                current_time = time.time()
                if hasattr(self, 'last_status_update'):
                    if current_time - self.last_status_update > 2:  # Every 2 seconds
                        self.publish_status()
                        self.last_status_update = current_time
                else:
                    self.last_status_update = current_time
                
                time.sleep_ms(50)  # 20Hz update rate
                
            except KeyboardInterrupt:
                log("Game stopped by user", "INFO")
                break
            except Exception as e:
                log(f"Game loop error: {e}", "ERROR")
                time.sleep(1)

    def publish_status(self):
        """Publish current status to MQTT"""
        if not self.mqtt_client:
            return
        
        status = {
            "team": "A",
            "game_state": self.game_state,
            "ready": self.ready,
            "team_b_ready": self.team_b_ready,
            "current_code": self.current_code,
            "target_weight": self.target_weight,
            "current_weight": self.pressure_sensor.read_weight() if self.game_state == "stage2" else 0,
            "stage1_completed": self.stage1_completed,
            "stage2_completed": self.stage2_completed,
            "timestamp": time.time()
        }
        
        try:
            self.mqtt_client.publish(b"escape_room/team_a/status", json.dumps(status))
        except Exception as e:
            log(f"Status publish error: {e}", "ERROR")

# Main execution
if __name__ == "__main__":
    try:
        # Check WiFi connection
        wlan = network.WLAN(network.STA_IF)
        if not wlan.isconnected():
            log("WiFi not connected! Check boot.py", "ERROR")
            raise SystemExit
        
        # Start the game
        game = EscapeRoomTeamA()
        game.run()
        
    except Exception as e:
        log(f"Main execution error: {e}", "ERROR")
        raise 