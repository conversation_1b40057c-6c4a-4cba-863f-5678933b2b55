# hardware/switches.py - COMPLETE UPDATED VERSION
"""
Rotary Switch Management - UPDATED FOR ESP32 NANO
Handle multiple rotary encoders with corrected direction and no click detection
"""

import time
from machine import Pin
from config.hardware_config import ROTARY_SWITCH_CONFIG
from utils.logger import log

class RotarySwitch:
    def __init__(self, clk_pin, dt_pin, name=""):
        """Initialize rotary switch using proven working logic"""
        self.name = name
        self.clk_pin = Pin(clk_pin, Pin.IN, Pin.PULL_UP)
        self.dt_pin = Pin(dt_pin, Pin.IN, Pin.PULL_UP)
        
        # Configuration
        self.config = ROTARY_SWITCH_CONFIG
        self.positions = self.config['positions']  # 0-9
        
        # State tracking
        self.position = 0
        self.last_clk_state = self.clk_pin.value()
        self.last_change_time = 0
        self.rotation_count = 0
        
        log(f"Rotary switch '{name}' initialized: CLK=GPIO{clk_pin}, DT=GPIO{dt_pin}", "DEBUG")
    
    def update(self):
        """Update rotary switch position using proven working logic"""
        current_clk_state = self.clk_pin.value()
        
        # Detect falling edge on CLK (proven working method)
        if self.last_clk_state == 1 and current_clk_state == 0:
            current_time = time.ticks_ms()
            
            # Check debounce time
            if time.ticks_diff(current_time, self.last_change_time) > self.config['debounce_ms']:
                
                # Read DT state to determine direction
                if self.dt_pin.value() == 1:
                    # DT is HIGH: increment (clockwise/right rotation)
                    direction = "CW"
                    new_position = (self.position + 1) % self.positions
                else:
                    # DT is LOW: decrement (counter-clockwise/left rotation)  
                    direction = "CCW"
                    new_position = (self.position - 1) % self.positions
                
                # Update position
                self.position = new_position
                self.rotation_count += 1
                self.last_change_time = current_time
                
                log(f"Rotary '{self.name}': {direction} → Position {self.position}", "DEBUG")
                
                self.last_clk_state = current_clk_state
                return {
                    'position': self.position,
                    'direction': direction,
                    'changed': True
                }
        
        self.last_clk_state = current_clk_state
        return {
            'position': self.position,
            'direction': None,
            'changed': False
        }
    
    def set_position(self, position):
        """Manually set position"""
        if 0 <= position < self.positions:
            old_position = self.position
            self.position = position
            log(f"Rotary '{self.name}' position set: {old_position} → {position}", "DEBUG")
            return True
        return False
    
    def get_position(self):
        """Get current position"""
        return self.position
    
    def reset(self):
        """Reset to position 0"""
        old_position = self.position
        self.position = 0
        self.rotation_count = 0
        log(f"Rotary '{self.name}' reset: {old_position} → 0", "INFO")
    
    def get_status(self):
        """Get switch status"""
        return {
            'name': self.name,
            'position': self.position,
            'rotation_count': self.rotation_count,
            'clk_state': self.clk_pin.value(),
            'dt_state': self.dt_pin.value(),
            'last_change_time': self.last_change_time
        }

class CodeEntry:
    """Handles the 3-digit code entry using rotary switches with MANUAL CONFIRMATION"""
    
    def __init__(self, hundreds_pin, tens_pin, ones_pin, confirm_pin=None):
        """Initialize 3-digit code entry system for ESP32 Nano"""
        # Get DT pin mapping from configuration
        dt_mapping = ROTARY_SWITCH_CONFIG['dt_pin_mapping']
        
        log(f"Initializing CodeEntry: H={hundreds_pin}, T={tens_pin}, O={ones_pin}, C={confirm_pin}", "INFO")
        log(f"DT pin mapping: {dt_mapping}", "DEBUG")
        
        # Initialize rotary switches with corrected DT pins
        self.hundreds_switch = RotarySwitch(
            hundreds_pin, 
            dt_mapping.get(hundreds_pin, hundreds_pin + 1), 
            "hundreds"
        )
        self.tens_switch = RotarySwitch(
            tens_pin, 
            dt_mapping.get(tens_pin, tens_pin + 1), 
            "tens"
        ) 
        self.ones_switch = RotarySwitch(
            ones_pin, 
            dt_mapping.get(ones_pin, ones_pin + 1), 
            "ones"
        )
        
        # Optional confirm button - MANUAL CONFIRMATION REQUIRED
        self.confirm_button = None
        self.last_button_state = 1
        self.last_button_time = 0
        
        if confirm_pin is not None:
            from machine import Pin
            self.confirm_button = Pin(confirm_pin, Pin.IN, Pin.PULL_UP)
            self.last_button_state = self.confirm_button.value()
            log(f"Manual confirm button initialized on GPIO{confirm_pin}", "INFO")
        
        # Code tracking
        self.current_code = [0, 0, 0]  # [hundreds, tens, ones]
        self.last_code = [0, 0, 0]
        self.confirmed_code = None
        self.attempt_count = 0
        self.auto_confirm = confirm_pin is None  # Only auto-confirm if no button
        self.last_update_time = 0
        
        log("Code entry system initialized for ESP32 Nano", "INFO")
        if self.auto_confirm:
            log("Auto-confirm mode enabled (no confirm button)", "INFO")
        else:
            log("Manual confirm mode enabled (button required)", "INFO")
    
    def update(self):
        """Update all switches and handle manual confirmation"""
        changes = []
        code_changed = False
        current_time = time.ticks_ms()
        
        # Update hundreds switch
        hundreds_result = self.hundreds_switch.update()
        if hundreds_result['changed']:
            self.current_code[0] = hundreds_result['position']
            changes.append(('hundreds', hundreds_result['position']))
            code_changed = True
        
        # Update tens switch
        tens_result = self.tens_switch.update()
        if tens_result['changed']:
            self.current_code[1] = tens_result['position']
            changes.append(('tens', tens_result['position']))
            code_changed = True
        
        # Update ones switch
        ones_result = self.ones_switch.update()
        if ones_result['changed']:
            self.current_code[2] = ones_result['position']
            changes.append(('ones', ones_result['position']))
            code_changed = True
        
        # Handle confirmation
        confirmation = False
        
        if code_changed:
            self.last_update_time = current_time
            log(f"Code changed to: {self.get_current_code():03d}", "DEBUG")
        
        # Manual confirmation via button (no auto-confirm)
        if self.confirm_button:
            button_confirmation = self.check_confirm_button()
            if button_confirmation:
                confirmation = True
        
        return {
            'changes': changes,
            'current_code': self.get_current_code(),
            'confirmed': confirmation,
            'confirmed_code': self.confirmed_code if confirmation else None,
            'code_changed': code_changed
        }
    
    def check_confirm_button(self):
        """Check if confirm button was pressed"""
        if not self.confirm_button:
            return False
            
        current_time = time.ticks_ms()
        button_state = self.confirm_button.value()
        
        # Detect button press (falling edge with debouncing)
        if (button_state != self.last_button_state and 
            time.ticks_diff(current_time, self.last_button_time) > 300):
            
            if button_state == 0:  # Button pressed (pull-up, so 0 = pressed)
                self.confirmed_code = self.get_current_code()
                self.attempt_count += 1
                self.last_button_time = current_time
                self.last_button_state = button_state
                log(f"Manual confirmed code: {self.confirmed_code:03d} (attempt #{self.attempt_count})", "INFO")
                return True
        
        self.last_button_state = button_state
        return False
    
    def get_current_code(self):
        """Get current 3-digit code as integer"""
        return int(f"{self.current_code[0]}{self.current_code[1]}{self.current_code[2]}")
    
    def get_individual_digits(self):
        """Get individual digit positions"""
        return {
            'hundreds': self.current_code[0],
            'tens': self.current_code[1],
            'ones': self.current_code[2]
        }
    
    def set_code(self, code):
        """Set the code manually (for testing)"""
        if 0 <= code <= 999:
            self.current_code[0] = (code // 100) % 10  # hundreds
            self.current_code[1] = (code // 10) % 10   # tens
            self.current_code[2] = code % 10           # ones
            
            # Update individual switches
            self.hundreds_switch.set_position(self.current_code[0])
            self.tens_switch.set_position(self.current_code[1])
            self.ones_switch.set_position(self.current_code[2])
            
            log(f"Code manually set to: {code:03d}", "INFO")
            return True
        return False
    
    def reset(self):
        """Reset all switches to 0"""
        self.hundreds_switch.reset()
        self.tens_switch.reset()
        self.ones_switch.reset()
        self.current_code = [0, 0, 0]
        self.last_code = [0, 0, 0]
        self.confirmed_code = None
        self.attempt_count = 0
        log("Code entry system reset to 000", "INFO")
    
    def get_status(self):
        """Get complete status of code entry system"""
        return {
            'current_code': self.get_current_code(),
            'individual_digits': self.get_individual_digits(),
            'confirmed_code': self.confirmed_code,
            'attempt_count': self.attempt_count,
            'auto_confirm': self.auto_confirm,
            'switches': {
                'hundreds': self.hundreds_switch.get_status(),
                'tens': self.tens_switch.get_status(),
                'ones': self.ones_switch.get_status()
            },
            'confirm_button_available': self.confirm_button is not None,
            'confirm_button_state': self.confirm_button.value() if self.confirm_button else None
        }
    
    def test_switches(self):
        """Test function to verify switch directions"""
        log("=== TESTING ROTARY SWITCHES ===", "INFO")
        log("Rotate each switch and verify:", "INFO")
        log("- RIGHT (clockwise) should INCREMENT (0→1→2...)", "INFO")
        log("- LEFT (counter-clockwise) should DECREMENT (9→8→7...)", "INFO")
        log("Current code: 000", "INFO")
        log("Press Ctrl+C to stop testing", "INFO")
        
        try:
            while True:
                result = self.update()
                if result['changes']:
                    code = result['current_code']
                    log(f"Code changed to: {code:03d}", "INFO")
                    for switch, position in result['changes']:
                        log(f"  {switch}: {position}", "INFO")
                
                time.sleep_ms(50)
                
        except KeyboardInterrupt:
            log("Switch testing stopped", "INFO")

# Test function for individual rotary switch
def test_single_rotary(clk_pin, dt_pin, name="test"):
    """Test a single rotary switch"""
    log(f"=== TESTING SINGLE ROTARY SWITCH: {name} ===", "INFO")
    log(f"CLK=GPIO{clk_pin}, DT=GPIO{dt_pin}", "INFO")
    log("Rotate and verify direction:", "INFO")
    log("- RIGHT (clockwise) should show 'CW' and increment", "INFO")
    log("- LEFT (counter-clockwise) should show 'CCW' and decrement", "INFO")
    log("Press Ctrl+C to stop", "INFO")
    
    switch = RotarySwitch(clk_pin, dt_pin, name)
    
    try:
        while True:
            result = switch.update()
            if result['changed']:
                log(f"Direction: {result['direction']}, Position: {result['position']}", "INFO")
            time.sleep_ms(10)
            
    except KeyboardInterrupt:
        log("Single switch testing stopped", "INFO")

# Example usage and testing code
if __name__ == "__main__":
    # Test configuration from hardware_config
    from config.hardware_config import TEAM_B_PINS
    
    log("Starting rotary switch testing...", "INFO")
    
    # Test individual switches first
    log("Testing hundreds switch (GPIO6/17):", "INFO")
    test_single_rotary(6, 17, "hundreds")
    
    # Uncomment to test other switches individually:
    # test_single_rotary(8, 18, "tens")
    # test_single_rotary(9, 21, "ones")
    
    # Test complete code entry system
    # code_entry = CodeEntry(
    #     TEAM_B_PINS['ROTARY_SWITCH_HUNDREDS'],
    #     TEAM_B_PINS['ROTARY_SWITCH_TENS'],
    #     TEAM_B_PINS['ROTARY_SWITCH_ONES'],
    #     TEAM_B_PINS['CONFIRM_BUTTON']
    # )
    # code_entry.test_switches()

"""
🔄 ROTARY ENCODER BEHAVIOR SUMMARY:

## Expected Behavior:
- RIGHT rotation (clockwise) → Number INCREASES (0→1→2...→9→0)
- LEFT rotation (counter-clockwise) → Number DECREASES (9→8→7...→0→9)
- No clicking needed - position updates on rotation
- Auto-confirm sends code attempt immediately

## Pin Assignments for Team B:
- Hundreds: CLK=GPIO6, DT=GPIO17
- Tens: CLK=GPIO8, DT=GPIO18  
- Ones: CLK=GPIO9, DT=GPIO21
- Confirm: GPIO16 (optional)

## Testing:
1. Run individual switch tests first
2. Verify each switch direction independently
3. Test complete 3-digit code entry
4. Confirm auto-confirm or manual confirm works

## Troubleshooting:
- If direction is reversed, swap CLK/DT connections
- If readings are noisy, check pull-up resistors
- If no response, verify pin assignments match config
- Check I2C interference if LCD also connected
"""
