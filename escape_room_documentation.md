# ESP32 Escape Room Project Documentation

## 📋 Project Overview

The ESP32 Escape Room is a collaborative puzzle game designed for two teams working together to escape from separate rooms. Each team has their own ESP32 microcontroller with specific hardware components and must communicate through a comprehensive web-based dashboard system via MQTT messaging to solve puzzles and complete challenges.

### 🎯 Game Objective

Two teams (Team A and Team B) are placed in separate rooms and must cooperate to complete a two-stage escape sequence:

1. **Stage 1**: Team A generates a 3-digit code that Team B must guess using rotary switches
2. **Stage 2**: Team B receives a target weight that Team A must achieve using a pressure sensor

Teams communicate through a professional Node-RED dashboard system with real-time web interfaces, creating a unique collaborative escape room experience with live monitoring and control capabilities.

---

## 🏗️ System Architecture

### Hardware Components

**Team A (Code Generator & Pressure Sensor):**

- ESP32 Nano microcontroller
- Pressure sensor (GPIO3 ADC)
- 20x4 LCD display (I2C)
- Start/Ready button (GPIO2)
- Status LED (GPIO4)
- Buzzer (GPIO5)

**Team B (Code Input & Weight Display):**

- ESP32 Nano microcontroller
- 3x Rotary encoders for code input (GPIO6, 8, 9)
- 20x4 LCD display (I2C)
- Confirm button (GPIO13)
- Status LED (GPIO14)
- Buzzer (GPIO7)

### Software Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    Team A       │    │   MQTT Broker   │    │    Team B       │
│    ESP32        │◄──►│  (Mosquitto)    │◄──►│    ESP32        │
│                 │    │ **************  │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ▲                        ▲                        ▲
         │                        │                        │
         ▼                        ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Team A        │    │     Admin       │    │   Team B        │
│  Web Interface  │◄──►│  Web Dashboard  │◄──►│  Web Interface  │
│  (Node-RED)     │    │   (Node-RED)    │    │   (Node-RED)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

---

## 🔧 Hardware Configuration

### Pin Assignments

#### Team A Pin Configuration

```python
TEAM_A_PINS = {
    'START_BUTTON': 2,      # GPIO2 - Ready button
    'STATUS_LED': 4,        # GPIO4 - Status indicator
    'BUZZER': 5,            # GPIO5 - Audio feedback
    'PRESSURE_SENSOR': 3,   # GPIO3 (ADC) - Weight sensor
    'LCD_SDA': 11,          # GPIO11 - I2C data
    'LCD_SCL': 12,          # GPIO12 - I2C clock
}
```

#### Team B Pin Configuration

```python
TEAM_B_PINS = {
    'CONFIRM_BUTTON': 13,           # GPIO13 - Code confirmation
    'STATUS_LED': 14,               # GPIO14 - Status indicator
    'BUZZER': 7,                    # GPIO7 - Audio feedback
    'ROTARY_SWITCH_HUNDREDS': 6,    # GPIO6 (CLK) - Hundreds digit
    'ROTARY_SWITCH_TENS': 8,        # GPIO8 (CLK) - Tens digit
    'ROTARY_SWITCH_ONES': 9,        # GPIO9 (CLK) - Ones digit
    'LCD_SDA': 47,                  # GPIO47 - I2C data
    'LCD_SCL': 38,                  # GPIO38 - I2C clock
}
```

#### Rotary Encoder Wiring

```python
ROTARY_SWITCH_CONFIG = {
    'dt_pin_mapping': {
        6: 17,   # Hundreds: CLK=GPIO6, DT=GPIO17
        8: 18,   # Tens: CLK=GPIO8, DT=GPIO18
        9: 21,   # Ones: CLK=GPIO9, DT=GPIO21
    }
}
```

### Hardware Setup Instructions

1. **ESP32 Connection**: Connect both ESP32s to WiFi network "FRITZ!Box 6660 Cable LE"
2. **LCD Displays**: Connect I2C 20x4 LCD displays to respective SDA/SCL pins
3. **Pressure Sensor**: Connect analog pressure sensor to GPIO3 on Team A
4. **Rotary Encoders**: Wire 3 rotary encoders to Team B with CLK/DT pin pairs
5. **Buttons/LEDs/Buzzers**: Connect according to pin configuration above

---

## 🎮 Game Flow & Mechanics

### Game States

```
waiting → ready → stage1 → stage2 → completed
    ↑                              ↓
    ←------------ reset ←-----------
                    ↓
                 failed (max attempts reached)
```

### Detailed Game Sequence

#### 1. Initialization Phase

- Both ESP32s connect to WiFi and MQTT broker
- Hardware components are initialized and calibrated
- LCD displays show "Team A/B Ready - Press to start"
- Web dashboards become accessible

#### 2. Ready Phase

- Teams press their respective ready buttons on ESP32s
- Status updates via MQTT: `escape_room/team_a/ready` and `escape_room/team_b/ready`
- When both teams ready: 3-second countdown begins
- LCD shows: "Both teams ready! Starting game in 3 seconds..."
- Web dashboards update in real-time

#### 3. Stage 1 - Code Communication

**Team A Actions:**

- Generates random 3-digit code (100-999)
- Displays code on LCD: `Code: 456`
- Code appears on Team A web interface
- Communicates code to Team B through 2-character messaging system

**Team B Actions:**

- Receives code through web-based communication system
- Uses rotary switches to input 3-digit code on ESP32
- LCD shows current input: `Code: 123` and `Tries left: 4`
- Presses confirm button to submit code attempt
- Web interface shows attempts remaining
- Has maximum 5 attempts before game failure

**Stage 1 Completion:**

- When correct code entered, advance to Stage 2
- MQTT message: `escape_room/stage2/target_weight`

#### 4. Stage 2 - Weight Challenge

**Team B Actions:**

- Receives target weight on LCD: `Weight needed: 2500g`
- Target weight displayed on Team B web interface
- Communicates target weight to Team A via web messaging

**Team A Actions:**

- Displays target weight: `Target: 2500g`
- Shows current pressure sensor reading: `Now: 0g`
- Uses pressure sensor to achieve target weight (±50g tolerance)
- Real-time weight feedback on LCD and web interface

**Stage 2 Completion:**

- When target weight achieved within tolerance
- Game completion with timing statistics
- MQTT message: `escape_room/game_completed`

#### 5. Game Completion

- Both teams show victory message: `GAME WON!`
- Display total completion time
- Audio celebration sequence
- Web interfaces show celebration effects
- Game statistics published for admin dashboard

---

## 🌐 Node-RED Dashboard System

The system includes a comprehensive web-based control and monitoring system built with Node-RED.

### **Admin Dashboard** (`http://localhost:1880/admin`)

**Features:**

- **Game Control**: Start/Reset/Pause buttons with real-time control
- **Team Status**: Live monitoring of both teams' current state
- **Code Display**: Shows the generated 3-digit code from Team A
- **Target Weight**: Displays the target weight for Stage 2
- **Game Timer**: Real-time countdown and elapsed time tracking
- **Communication Log**: Complete history of inter-team messages
- **Progress Log**: Detailed game progress and events
- **System Health**: MQTT connectivity and hardware status indicators

**Visual Elements:**

- Professional dark theme with team-specific color coding
- Real-time LED indicators for team states
- Animated progress bars and status updates
- Responsive design for desktop and mobile

### **Team A Interface** (`http://localhost:1880/team-a`)

**Features:**

- **Secret Code Display**: Large, highlighted display of generated code
- **Game Status**: Current game state with animated indicators
- **Communication Panel**: 2-character messaging system to Team B
- **Message History**: Log of communications with Team B
- **Connection Status**: Real-time WebSocket connection indicator

**User Experience:**

- Green/teal color scheme representing "Code Masters"
- Pulse animations for active game elements
- Real-time updates without page refresh
- Touch-friendly interface for tablets

### **Team B Interface** (`http://localhost:1880/team-b`)

**Features:**

- **Code Input Instructions**: Clear directions for using rotary switches
- **Attempts Counter**: Visual indicator of remaining attempts
- **Target Weight Display**: Large display of weight target for Stage 2
- **Communication Panel**: 2-character messaging system to Team A
- **Message History**: Log of communications with Team A
- **Game Status**: Current game state with animated indicators

**User Experience:**

- Purple/pink color scheme representing "Code Breakers"
- Attempt indicators with visual feedback
- Real-time updates without page refresh
- Mobile-responsive design

### **Technical Implementation**

- **WebSocket Communication**: Real-time bidirectional communication
- **MQTT Integration**: Seamless integration with ESP32 systems
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Professional UI**: Modern CSS animations and effects
- **Error Handling**: Automatic reconnection and fallback modes

---

## 📡 MQTT Communication Protocol

### Network Configuration

- **WiFi SSID**: "FRITZ!Box 6660 Cable LE"
- **WiFi Password**: "02593654638468919391"
- **MQTT Broker**: **************:1883
- **Protocol**: MQTT v3.1.1 (unencrypted)

### MQTT Topic Structure

#### Status Topics

```
escape_room/team_a/status          # Team A periodic status
escape_room/team_b/status          # Team B periodic status
escape_room/team_a/ready           # Team A ready signal
escape_room/team_b/ready           # Team B ready signal
```

#### Game Control Topics

```
escape_room/game_control           # Admin game control (start/reset)
escape_room/stage1/code_generated  # Generated code for Stage 1
escape_room/team_b/code_attempt    # Code attempts from Team B
escape_room/stage2/target_weight   # Target weight for Stage 2
escape_room/game_completed         # Game completion notification
escape_room/game_failed            # Game failure notification
```

#### Communication Topics

```
escape_room/communication          # Inter-team messaging
escape_room/admin                  # Admin dashboard updates
```

### Message Formats

#### Ready Status Message

```json
{
  "team": "A",
  "ready": true,
  "timestamp": 1672531200
}
```

#### Code Attempt Message

```json
{
  "code": 456,
  "attempt": 3,
  "attempts_remaining": 2,
  "timestamp": 1672531200
}
```

#### Game Completion Message

```json
{
  "game_completed": true,
  "stage1_time": 125.5,
  "stage2_time": 89.2,
  "total_time": 214.7,
  "timestamp": 1672531200
}
```

#### Team Status Message

```json
{
  "team": "A",
  "game_state": "stage1",
  "ready": true,
  "current_code": 456,
  "target_weight": null,
  "current_weight": 0,
  "stage1_completed": false,
  "stage2_completed": false,
  "timestamp": 1672531200
}
```

---

## 💻 Software Components

### File Structure

```
escape_room_project/
├── team_a/
│   ├── main.py                    # Main game logic for Team A
│   ├── boot.py                    # WiFi connection setup
│   ├── secrets.py                 # WiFi credentials
│   ├── config/
│   │   └── hardware_config.py     # Hardware pin configurations
│   ├── hardware/
│   │   ├── pressure_sensor.py     # Pressure sensor driver
│   │   └── lcd.py                 # LCD display driver
│   ├── utils/
│   │   ├── logger.py              # Logging utilities
│   │   └── helpers.py             # Helper functions
│   └── umqtt/
│       └── simple.py              # MQTT client library
├── team_b/
│   ├── main.py                    # Main game logic for Team B
│   ├── boot.py                    # WiFi connection setup
│   ├── secrets.py                 # WiFi credentials
│   ├── config/
│   │   └── hardware_config.py     # Hardware pin configurations
│   ├── hardware/
│   │   ├── switches.py            # Rotary switch driver
│   │   └── lcd.py                 # LCD display driver
│   ├── utils/
│   │   ├── logger.py              # Logging utilities
│   │   └── helpers.py             # Helper functions
│   └── umqtt/
│       └── simple.py              # MQTT client library
└── node_red/
    ├── flow.json                  # Node-RED flow configuration
    ├── admin.html                 # Admin dashboard template
    ├── team-a.html               # Team A interface template
    └── team-b.html               # Team B interface template
```

### Key Classes and Functions

#### Team A - EscapeRoomTeamA Class

```python
class EscapeRoomTeamA:
    def __init__(self)                  # Initialize hardware and MQTT
    def check_button(self)              # Handle ready button input
    def set_ready(self)                 # Mark team as ready
    def start_game(self)                # Begin Stage 1
    def handle_code_attempt(self, data) # Process Team B code attempts
    def check_pressure_sensor(self)     # Monitor pressure for Stage 2
    def update_display(self)            # Update LCD based on game state
    def play_sound(self, sound_type)    # Audio feedback system
    def run(self)                       # Main game loop
```

#### Team B - EscapeRoomTeamB Class

```python
class EscapeRoomTeamB:
    def __init__(self)                  # Initialize hardware and MQTT
    def check_ready_button(self)        # Handle ready button input
    def set_ready(self)                 # Mark team as ready
    def start_game(self)                # Begin Stage 1
    def check_code_input(self)          # Monitor rotary switch input
    def update_display(self)            # Update LCD based on game state
    def play_sound(self, sound_type)    # Audio feedback system
    def run(self)                       # Main game loop
```

#### Hardware Drivers

**PressureSensor Class (Team A):**

- Calibration and zero-point adjustment
- Raw ADC reading to weight conversion
- Moving average filtering for stability
- Voltage range: 1.0V (10kg) to 3.3V (0kg)

**CodeEntry Class (Team B):**

- Three rotary encoder management
- Debounced rotation detection
- Manual confirmation system
- Position tracking (0-9 per encoder)

**LCD Class (Both Teams):**

- I2C communication protocol
- 20x4 character display support
- Cursor positioning and text printing
- Display control (on/off, cursor, backlight)

---

## 🔧 Installation & Setup

### Prerequisites

- 2x ESP32 Nano microcontrollers
- MicroPython firmware installed on both ESP32s
- MQTT broker (Mosquitto) running on **************
- Node-RED installed and running on the same machine

### Installation Steps

#### 1. ESP32 Firmware Setup

```bash
# Flash MicroPython firmware to ESP32
esptool.py --chip esp32 --port /dev/ttyUSB0 erase_flash
esptool.py --chip esp32 --port /dev/ttyUSB0 write_flash -z 0x1000 esp32-micropython.bin
```

#### 2. File Upload

Upload the complete file structure to each ESP32:

- Team A files to first ESP32
- Team B files to second ESP32
- Shared files (config, utils) to both

#### 3. MQTT Broker Setup

```bash
# Install Mosquitto
sudo apt install mosquitto mosquitto-clients

# Start broker
mosquitto -v

# Test connection
mosquitto_pub -h ************** -t test/topic -m "hello"
mosquitto_sub -h ************** -t test/topic
```

#### 4. Node-RED Setup

```bash
# Install Node-RED (if not already installed)
npm install -g --unsafe-perm node-red

# Start Node-RED
node-red

# Import the flow
# 1. Open http://localhost:1880
# 2. Import the flow.json file
# 3. Deploy the flow
```

#### 5. Hardware Connections

Follow the pin configuration tables above to connect:

- 20x4 LCD displays via I2C
- Pressure sensor to Team A GPIO3
- Rotary encoders to Team B GPIOs 6,8,9
- Buttons, LEDs, and buzzers as specified

#### 6. Access Web Interfaces

- Admin Dashboard: `http://localhost:1880/admin`
- Team A Interface: `http://localhost:1880/team-a`
- Team B Interface: `http://localhost:1880/team-b`

---

## 🧪 Testing & Troubleshooting

### System Testing Procedure

#### 1. Network Connectivity

```bash
# Test MQTT connection
mosquitto_sub -h ************** -t "escape_room/#" -v

# Verify ESP32 WiFi connection
# Check serial output from ESP32 boot process
```

#### 2. Web Interface Testing

- Access all three web interfaces
- Verify WebSocket connections (green status indicators)
- Test admin game control buttons
- Test team communication panels

#### 3. Hardware Component Testing

```python
# Test pressure sensor
from hardware.pressure_sensor import PressureSensor
sensor = PressureSensor(3)
sensor.calibrate_zero()
while True:
    print(f"Weight: {sensor.read_weight()}g")
    time.sleep(1)

# Test rotary switches
from hardware.switches import CodeEntry
code_entry = CodeEntry(6, 8, 9, 13)
code_entry.test_switches()
```

### Common Issues & Solutions

#### 1. MQTT Connection Failures

**Symptoms:** `[Errno 104] ECONNRESET` errors, red connection status
**Solutions:**

- Verify broker IP address (**************)
- Check if Mosquitto is running: `mosquitto -v`
- Test network connectivity: `ping **************`
- Verify firewall settings allow port 1883

#### 2. Web Interface Issues

**Symptoms:** Blank pages, connection errors, WebSocket failures
**Solutions:**

- Ensure Node-RED is running: `node-red`
- Check Node-RED flow is deployed properly
- Verify port 1880 is accessible
- Check browser console for JavaScript errors

#### 3. Rotary Encoder Direction Issues

**Symptoms:** Clockwise rotation decrements instead of increments
**Solutions:**

- Swap CLK and DT pin connections
- Verify pin mapping in `ROTARY_SWITCH_CONFIG`
- Test individual encoders with `test_single_rotary()`

#### 4. Pressure Sensor Calibration

**Symptoms:** Incorrect weight readings or constant 3.300V values
**Solutions:**

- Check ADC connection to GPIO3
- Verify sensor power supply (3.3V/5V)
- Recalibrate zero point: `sensor.calibrate_zero()`
- Check for floating/disconnected sensor

#### 5. LCD Display Issues

**Symptoms:** Blank display or garbled text
**Solutions:**

- Verify I2C address (typically 0x27)
- Check SDA/SCL pin connections for 20x4 displays
- Test I2C communication: `i2c.scan()`
- Verify power supply to LCD module

### Testing Modes

#### 1. Local Test Mode (No MQTT)

When MQTT broker is unavailable, the system automatically switches to local test mode:

- Both teams auto-start after 3 seconds when ready
- All game logic functions normally
- Web interfaces will show disconnected status
- No inter-team communication (manual code sharing required)

#### 2. Web Interface Integration Testing

- Start both ESP32s and verify they appear in admin dashboard
- Test game start/reset from admin interface
- Verify real-time status updates across all interfaces
- Test team communication messaging system
- Confirm celebration effects on game completion

---

## 🎯 System Requirements & Specifications

### Hardware Requirements

- **ESP32 RAM**: 520KB SRAM, 448KB ROM
- **Flash Memory**: Minimum 4MB for MicroPython and application
- **WiFi**: 802.11 b/g/n, 2.4GHz band
- **Power Supply**: 3.0V to 3.6V for ESP32s
- **LCD Displays**: 20x4 I2C LCD modules (0x27 address)
- **Pressure Sensor**: Analog output, 0-10kg range recommended

### Network Requirements

- **WiFi Network**: 2.4GHz capable router
- **MQTT Broker**: Mosquitto or equivalent on port 1883
- **Node-RED Server**: Port 1880 for web interfaces
- **Bandwidth**: Minimal (<1KB/s per ESP32)

### Game Performance

- **Game Loop Frequency**: 20Hz (50ms intervals)
- **MQTT Status Updates**: Every 2 seconds
- **Button Debounce Time**: 500ms
- **Pressure Sensor Accuracy**: ±50g tolerance
- **Rotary Encoder Resolution**: 10 positions per encoder
- **Maximum Game Duration**: 30 minutes (configurable timeout)
- **WebSocket Latency**: <100ms local network

_Last Updated: December 2024_
_Current Implementation: Fully Functional System_
