<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🎯 Team B - Escape Room Interface</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      html,
      body {
        height: 100%;
        overflow: hidden;
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          sans-serif;
      }

      body {
        background: linear-gradient(
          135deg,
          #2c1810 0%,
          #5c2a6b 25%,
          #764ba2 50%,
          #667eea 75%,
          #f093fb 100%
        );
        background-attachment: fixed;
        color: #ffffff;
        display: flex;
        flex-direction: column;
      }

      .header {
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(20px);
        border-bottom: 2px solid rgba(240, 147, 251, 0.5);
        padding: 12px 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        flex-shrink: 0;
      }

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 1400px;
        margin: 0 auto;
      }

      .team-title {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .team-title h1 {
        font-size: 1.8rem;
        font-weight: 700;
        background: linear-gradient(135deg, #f093fb, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 0 30px rgba(240, 147, 251, 0.3);
      }

      .team-badge {
        background: linear-gradient(135deg, #f093fb, #764ba2);
        color: #fff;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .connection-status {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
      }

      .status-connected {
        background: rgba(240, 147, 251, 0.2);
        color: #f093fb;
        border: 1px solid rgba(240, 147, 251, 0.5);
      }

      .status-disconnected {
        background: rgba(255, 107, 107, 0.2);
        color: #ff6b6b;
        border: 1px solid rgba(255, 107, 107, 0.5);
        animation: pulse-red 2s infinite;
      }

      @keyframes pulse-red {
        0%,
        100% {
          opacity: 1;
        }

        50% {
          opacity: 0.6;
        }
      }

      .main-container {
        flex: 1;
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto 1fr;
        gap: 16px;
        padding: 16px;
        min-height: 0;
      }

      .status-card {
        grid-column: 1 / -1;
        background: rgba(255, 255, 255, 0.08);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      }

      .status-header {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        margin-bottom: 16px;
      }

      .status-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
      }

      .game-state {
        font-size: 1.5rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 2px;
      }

      .state-waiting .status-icon {
        background: #ffaa00;
        color: #000;
      }

      .state-waiting .game-state {
        color: #ffaa00;
        text-shadow: 0 0 20px #ffaa00;
      }

      .state-ready .status-icon {
        background: #ffff00;
        color: #000;
      }

      .state-ready .game-state {
        color: #ffff00;
        text-shadow: 0 0 20px #ffff00;
      }

      .state-stage1 .status-icon {
        background: #00aaff;
        color: #fff;
      }

      .state-stage1 .game-state {
        color: #00aaff;
        text-shadow: 0 0 20px #00aaff;
      }

      .state-stage2 .status-icon {
        background: #aa00ff;
        color: #fff;
      }

      .state-stage2 .game-state {
        color: #aa00ff;
        text-shadow: 0 0 20px #aa00ff;
      }

      .state-completed .status-icon {
        background: #00ff00;
        color: #000;
      }

      .state-completed .game-state {
        color: #00ff00;
        text-shadow: 0 0 20px #00ff00;
      }

      .state-failed .status-icon {
        background: #ff0000;
        color: #fff;
      }

      .state-failed .game-state {
        color: #ff0000;
        text-shadow: 0 0 20px #ff0000;
      }

      .status-description {
        text-align: center;
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.95rem;
        line-height: 1.4;
      }

      .attempts-indicator {
        display: flex;
        justify-content: center;
        margin-top: 12px;
        gap: 8px;
      }

      .attempt-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
      }

      .attempt-dot.used {
        background: #ff6b6b;
      }

      .attempt-dot.remaining {
        background: #ffaa00;
        box-shadow: 0 0 10px rgba(255, 170, 0, 0.5);
      }

      .card {
        background: rgba(255, 255, 255, 0.08);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: column;
        transition: all 0.3s ease;
        overflow: hidden;
      }

      .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        border-color: rgba(240, 147, 251, 0.4);
      }

      .card-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .card-icon {
        font-size: 1.2rem;
      }

      .card-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #f093fb;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .puzzle-display {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        background: linear-gradient(
          135deg,
          rgba(240, 147, 251, 0.1),
          rgba(118, 75, 162, 0.1)
        );
        border: 2px solid rgba(240, 147, 251, 0.3);
        border-radius: 12px;
        padding: 24px;
        animation: glow-border 3s ease-in-out infinite;
      }

      @keyframes glow-border {
        0%,
        100% {
          border-color: rgba(240, 147, 251, 0.3);
          box-shadow: 0 0 20px rgba(240, 147, 251, 0.1);
        }

        50% {
          border-color: rgba(240, 147, 251, 0.6);
          box-shadow: 0 0 30px rgba(240, 147, 251, 0.3);
        }
      }

      .puzzle-label {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 12px;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .target-value {
        font-size: 3rem;
        font-weight: 900;
        color: #f093fb;
        font-family: "JetBrains Mono", "Courier New", monospace;
        letter-spacing: 8px;
        text-shadow: 0 0 30px rgba(240, 147, 251, 0.5);
        margin-bottom: 12px;
        animation: pulse-glow 2s ease-in-out infinite;
      }

      @keyframes pulse-glow {
        0%,
        100% {
          text-shadow: 0 0 30px rgba(240, 147, 251, 0.5);
        }

        50% {
          text-shadow: 0 0 40px rgba(240, 147, 251, 0.8);
        }
      }

      .puzzle-instruction {
        font-size: 0.85rem;
        color: #ffaa00;
        font-weight: 500;
        line-height: 1.4;
      }

      .communication-card {
        display: flex;
        flex-direction: column;
      }

      .comm-input-section {
        background: rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(240, 147, 251, 0.3);
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 16px;
      }

      .comm-label {
        font-size: 0.85rem;
        color: #f093fb;
        font-weight: 600;
        margin-bottom: 12px;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .char-inputs-container {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 12px;
      }

      .char-inputs {
        display: flex;
        gap: 8px;
      }

      .char-input {
        width: 48px;
        height: 48px;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(240, 147, 251, 0.4);
        border-radius: 8px;
        color: #fff;
        font-size: 1.5rem;
        font-weight: 700;
        text-align: center;
        font-family: "JetBrains Mono", monospace;
        text-transform: uppercase;
        transition: all 0.3s ease;
      }

      .char-input:focus {
        outline: none;
        border-color: #f093fb;
        background: rgba(240, 147, 251, 0.1);
        box-shadow: 0 0 20px rgba(240, 147, 251, 0.3);
        transform: scale(1.05);
      }

      .char-input::placeholder {
        color: rgba(255, 255, 255, 0.3);
      }

      .char-counter {
        font-size: 0.8rem;
        color: #ffaa00;
        font-weight: 500;
      }

      .send-button {
        background: linear-gradient(135deg, #f093fb, #764ba2);
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        cursor: pointer;
        transition: all 0.3s ease;
        align-self: flex-end;
      }

      .send-button:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
      }

      .send-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background: #666;
      }

      .messages-area {
        flex: 1;
        background: rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 16px;
        overflow-y: auto;
        min-height: 0;
      }

      .messages-area::-webkit-scrollbar {
        width: 4px;
      }

      .messages-area::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
      }

      .messages-area::-webkit-scrollbar-thumb {
        background: #f093fb;
        border-radius: 2px;
      }

      .message {
        background: rgba(118, 75, 162, 0.2);
        border-left: 3px solid #764ba2;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 12px;
        animation: slide-in 0.3s ease;
      }

      @keyframes slide-in {
        from {
          opacity: 0;
          transform: translateX(-20px);
        }

        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      .message-time {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.5);
        margin-bottom: 4px;
      }

      .message-content {
        font-size: 0.9rem;
        color: #fff;
        font-weight: 500;
      }

      .no-messages {
        text-align: center;
        color: rgba(255, 255, 255, 0.4);
        font-style: italic;
        margin-top: 40px;
      }

      .hidden {
        display: none !important;
      }

      .pulse {
        animation: pulse-scale 2s ease-in-out infinite;
      }

      @keyframes pulse-scale {
        0%,
        100% {
          transform: scale(1);
        }

        50% {
          transform: scale(1.02);
        }
      }

      /* Celebration effects */
      .celebration {
        animation: celebration-hue 3s ease-in-out;
      }

      @keyframes celebration-hue {
        0%,
        100% {
          filter: hue-rotate(0deg);
        }

        25% {
          filter: hue-rotate(90deg);
        }

        50% {
          filter: hue-rotate(180deg);
        }

        75% {
          filter: hue-rotate(270deg);
        }
      }

      .confetti {
        position: fixed;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        pointer-events: none;
        z-index: 1000;
        animation: confetti-fall linear forwards;
      }

      @keyframes confetti-fall {
        0% {
          transform: translateY(-100vh) rotate(0deg);
          opacity: 1;
        }

        100% {
          transform: translateY(100vh) rotate(720deg);
          opacity: 0;
        }
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .main-container {
          grid-template-columns: 1fr;
          gap: 12px;
          padding: 12px;
        }

        .header-content {
          flex-direction: column;
          gap: 8px;
        }

        .team-title h1 {
          font-size: 1.4rem;
        }

        .card {
          padding: 16px;
        }

        .target-value {
          font-size: 2rem;
          letter-spacing: 4px;
        }

        .char-input {
          width: 40px;
          height: 40px;
          font-size: 1.2rem;
        }

        .comm-input-section {
          padding: 12px;
        }

        .char-inputs-container {
          flex-direction: column;
          align-items: stretch;
          gap: 8px;
        }

        .char-inputs {
          justify-content: center;
        }
      }
    </style>
  </head>

  <body>
    <header class="header">
      <div class="header-content">
        <div class="team-title">
          <h1>🎯 TEAM B</h1>
          <div class="team-badge">Code Breakers</div>
        </div>
        <div class="connection-status" id="connectionStatus">
          <span>🔄</span>
          <span>Connecting...</span>
        </div>
      </div>
    </header>

    <main class="main-container">
      <div class="status-card">
        <div class="status-header" id="statusHeader">
          <div class="status-icon">⏳</div>
          <div class="game-state" id="gameState">WAITING</div>
        </div>
        <div class="status-description" id="statusDescription">
          Press your confirm button on the ESP32 to begin the escape room
          challenge...
        </div>
        <div
          class="attempts-indicator"
          id="attemptsIndicator"
          style="display: none"
        >
          <div class="attempt-dot remaining"></div>
          <div class="attempt-dot remaining"></div>
          <div class="attempt-dot remaining"></div>
          <div class="attempt-dot remaining"></div>
          <div class="attempt-dot remaining"></div>
        </div>
      </div>

      <div class="card" id="codeCard">
        <div class="card-header">
          <div class="card-icon">🔐</div>
          <div class="card-title">Code Breaking</div>
        </div>
        <div class="puzzle-display">
          <div class="puzzle-label">Your Mission</div>
          <div class="puzzle-instruction">
            🎯 Get the 3-digit code from Team A<br />
            🔄 Use rotary switches on your ESP32<br />
            ✅ Press confirm to submit each attempt
          </div>
        </div>
      </div>

      <div class="card communication-card">
        <div class="card-header">
          <div class="card-icon">💬</div>
          <div class="card-title">Communication</div>
        </div>
        <div class="comm-input-section">
          <div class="comm-label">Message to Team A</div>
          <div class="char-inputs-container">
            <div class="char-inputs">
              <input
                type="text"
                class="char-input"
                id="char1"
                maxlength="1"
                placeholder="5"
              />
              <input
                type="text"
                class="char-input"
                id="char2"
                maxlength="1"
                placeholder="K"
              />
            </div>
            <div class="char-counter"><span id="charCount">0</span>/2</div>
          </div>
          <button class="send-button" id="sendBtn" onclick="sendMessage()">
            📤 Send Message
          </button>
        </div>
        <div class="messages-area" id="messagesArea">
          <div class="no-messages">No messages from Team A yet...</div>
        </div>
      </div>

      <div class="card" id="weightCard">
        <div class="card-header">
          <div class="card-icon">⚖️</div>
          <div class="card-title">Weight Target</div>
        </div>
        <div class="puzzle-display">
          <div class="puzzle-label">Target Weight</div>
          <div class="target-value" id="targetWeight">---.--kg</div>
          <div class="puzzle-instruction">
            Tell Team A to achieve this weight!
          </div>
        </div>
      </div>
    </main>

    <script>
      let ws;
      let celebrationShown = false;
      let currentGameState = "waiting";
      let previousGameState = "waiting";

      function connectWebSocket() {
        const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
        const wsUrl = `${protocol}//${window.location.host}/ws/team-b`;

        ws = new WebSocket(wsUrl);

        ws.onopen = function () {
          updateConnectionStatus(true);
          ws.send(JSON.stringify({ action: "get_status" }));
        };

        ws.onclose = function () {
          updateConnectionStatus(false);
          setTimeout(connectWebSocket, 3000);
        };

        ws.onerror = function () {
          updateConnectionStatus(false);
        };

        ws.onmessage = function (event) {
          try {
            const data = JSON.parse(event.data);
            handleMessage(data);
          } catch (e) {
            console.error("WebSocket message error:", e);
          }
        };
      }

      function updateConnectionStatus(connected) {
        const statusEl = document.getElementById("connectionStatus");
        if (connected) {
          statusEl.innerHTML = "<span>🟢</span><span>Connected</span>";
          statusEl.className = "connection-status status-connected";
        } else {
          statusEl.innerHTML = "<span>🔴</span><span>Disconnected</span>";
          statusEl.className = "connection-status status-disconnected";
        }
      }

      function handleMessage(data) {
        console.log("Team B received:", data); // Debug log

        switch (data.action) {
          case "status_update":
          case "heartbeat":
            updateStatus(data);
            break;
          case "target_weight_received":
            displayTargetWeight(data.target_weight);
            break;
          case "game_completed":
            showCelebration();
            break;
          case "game_failed":
            updateGameState({ game_state: "failed" });
            break;
        }
      }

      function updateStatus(data) {
        if (data.team_status) {
          updateGameState(data.team_status);
        }
        if (data.target_weight) {
          displayTargetWeight(data.target_weight);
        }
        if (data.messages) {
          updateMessages(data.messages);
        }
      }

      function updateGameState(status) {
        const gameState = status.game_state || "waiting";
        const attemptsRemaining = status.attempts_remaining || 5;
        previousGameState = currentGameState;
        currentGameState = gameState;

        const stateEl = document.getElementById("gameState");
        const headerEl = document.getElementById("statusHeader");
        const descEl = document.getElementById("statusDescription");
        const attemptsEl = document.getElementById("attemptsIndicator");

        stateEl.textContent = gameState.toUpperCase();
        headerEl.className = `status-header state-${gameState}`;

        const icons = {
          waiting: "⏳",
          ready: "🟡",
          stage1: "🔐",
          stage2: "⚖️",
          completed: "🎉",
          failed: "💀",
        };

        headerEl.querySelector(".status-icon").textContent =
          icons[gameState] || "❓";

        const descriptions = {
          waiting:
            "Press your confirm button on the ESP32 to begin the escape room challenge...",
          ready:
            "Both teams are ready! The game will start in a few seconds...",
          stage1:
            "Get the secret code from Team A and enter it using your rotary switches!",
          stage2:
            "You received a target weight. Communicate it to Team A so they can achieve it!",
          completed: "🎉 MISSION ACCOMPLISHED! You successfully escaped! 🎉",
          failed:
            "💀 Mission failed. No attempts remaining. Better luck next time!",
        };

        descEl.textContent = descriptions[gameState] || "Unknown game state...";

        // Update attempts indicator
        if (gameState === "stage1") {
          attemptsEl.style.display = "flex";
          updateAttemptsDisplay(attemptsRemaining);
        } else {
          attemptsEl.style.display = "none";
        }

        // Show/hide panels based on game state
        const codeCard = document.getElementById("codeCard");
        const weightCard = document.getElementById("weightCard");

        if (gameState === "stage1") {
          codeCard.classList.remove("hidden");
          codeCard.classList.add("pulse");
          weightCard.classList.add("hidden");
        } else if (gameState === "stage2") {
          codeCard.classList.add("hidden");
          weightCard.classList.remove("hidden");
          weightCard.classList.add("pulse");
        } else if (
          gameState === "completed" &&
          previousGameState !== "completed"
        ) {
          codeCard.classList.add("hidden");
          weightCard.classList.add("hidden");
          showCelebration();
        } else {
          codeCard.classList.add("hidden");
          weightCard.classList.add("hidden");
        }
      }

      function updateAttemptsDisplay(remaining) {
        const dots = document.querySelectorAll(".attempt-dot");
        dots.forEach((dot, index) => {
          if (index < 5 - remaining) {
            dot.className = "attempt-dot used";
          } else {
            dot.className = "attempt-dot remaining";
          }
        });
      }

      function displayTargetWeight(weight) {
        const weightEl = document.getElementById("targetWeight");
        if (weight) {
          const weightKg = Math.floor(weight / 1000);
          weightEl.textContent = `${weightKg}kg`;
        } else {
          weightEl.textContent = "--kg";
        }
      }

      function sendMessage() {
        const char1 = document.getElementById("char1").value.trim();
        const char2 = document.getElementById("char2").value.trim();
        const message = char1 + char2;

        if (message && ws && ws.readyState === WebSocket.OPEN) {
          ws.send(
            JSON.stringify({
              action: "send_message",
              message: message,
            })
          );

          document.getElementById("char1").value = "";
          document.getElementById("char2").value = "";
          updateCharCount();

          const btn = document.getElementById("sendBtn");
          btn.disabled = true;
          setTimeout(() => (btn.disabled = false), 1000);
        }
      }

      function updateCharCount() {
        const char1 = document.getElementById("char1").value;
        const char2 = document.getElementById("char2").value;
        const count = char1.length + char2.length;
        document.getElementById("charCount").textContent = count;
        document.getElementById("sendBtn").disabled = count === 0;
      }

      function setupInputs() {
        const char1 = document.getElementById("char1");
        const char2 = document.getElementById("char2");

        char1.addEventListener("input", function () {
          this.value = this.value.toUpperCase();
          updateCharCount();
          if (this.value.length === 1) char2.focus();
        });

        char2.addEventListener("input", function () {
          this.value = this.value.toUpperCase();
          updateCharCount();
        });

        char2.addEventListener("keydown", function (e) {
          if (e.key === "Backspace" && !this.value) char1.focus();
        });

        [char1, char2].forEach((input) => {
          input.addEventListener("keypress", function (e) {
            if (e.key === "Enter") sendMessage();
          });
        });
      }

      function updateMessages(messages) {
        const area = document.getElementById("messagesArea");

        if (!messages || messages.length === 0) {
          area.innerHTML =
            '<div class="no-messages">No messages from Team A yet...</div>';
          return;
        }

        let html = "";
        messages.slice(-5).forEach((msg) => {
          const time = new Date(msg.timestamp).toLocaleTimeString();
          html += `
                    <div class="message">
                        <div class="message-time">[${time}] From Team A:</div>
                        <div class="message-content">${msg.message}</div>
                    </div>
                `;
        });

        area.innerHTML = html;
        area.scrollTop = area.scrollHeight;
      }

      function showCelebration() {
        if (celebrationShown) return;
        document.body.classList.add("celebration");
        createConfetti();
        celebrationShown = true;
        setTimeout(() => {
          document.body.classList.remove("celebration");
          // Reset the flag after 10 seconds in case the game is restarted
          setTimeout(() => {
            celebrationShown = false;
          }, 10000);
        }, 3000);
      }

      function createConfetti() {
        const colors = ["#38ef7d", "#11998e", "#ffaa00", "#ff6b6b", "#4ecdc4"];
        for (let i = 0; i < 50; i++) {
          setTimeout(() => {
            const confetti = document.createElement("div");
            confetti.className = "confetti";
            confetti.style.left = Math.random() * 100 + "vw";
            confetti.style.backgroundColor =
              colors[Math.floor(Math.random() * colors.length)];
            confetti.style.animationDuration = Math.random() * 3 + 2 + "s";
            document.body.appendChild(confetti);
            setTimeout(() => confetti.remove(), 5000);
          }, i * 100);
        }
      }

      // Initialize
      window.addEventListener("load", () => {
        connectWebSocket();
        setupInputs();
        updateCharCount();
        document.getElementById("codeCard").classList.add("hidden");
        document.getElementById("weightCard").classList.add("hidden");
      });
    </script>
  </body>
</html>
