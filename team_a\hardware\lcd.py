# hardware/lcd.py
# LCD Display driver for I2C LCD displays (PCF8574)

import time

class LCD:
    """I2C LCD Display driver for 16x2 or 20x4 displays"""
    
    def __init__(self, i2c, address=0x27, width=16, height=2):
        """Initialize LCD display"""
        self.i2c = i2c
        self.address = address
        self.width = width
        self.height = height
        
        # LCD commands
        self.LCD_CLEARDISPLAY = 0x01
        self.LCD_RETURNHOME = 0x02
        self.LCD_ENTRYMODESET = 0x04
        self.LCD_DISPLAYCONTROL = 0x08
        self.LCD_CURSORSHIFT = 0x10
        self.LCD_FUNCTIONSET = 0x20
        self.LCD_SETCGRAMADDR = 0x40
        self.LCD_SETDDRAMADDR = 0x80
        
        # Flags for display entry mode
        self.LCD_ENTRYRIGHT = 0x00
        self.LCD_ENTRYLEFT = 0x02
        self.LCD_ENTRYSHIFTINCREMENT = 0x01
        self.LCD_ENTRYSHIFTDECREMENT = 0x00
        
        # Flags for display on/off control
        self.LCD_DISPLAYON = 0x04
        self.LCD_DISPLAYOFF = 0x00
        self.LCD_CURSORON = 0x02
        self.LCD_CURSOROFF = 0x00
        self.LCD_BLINKON = 0x01
        self.LCD_BLINKOFF = 0x00
        
        # Flags for function set
        self.LCD_4BITMODE = 0x00
        self.LCD_8BITMODE = 0x10
        self.LCD_1LINE = 0x00
        self.LCD_2LINE = 0x08
        self.LCD_5x8DOTS = 0x00
        self.LCD_5x10DOTS = 0x04
        
        # Backlight control
        self.LCD_BACKLIGHT = 0x08
        self.LCD_NOBACKLIGHT = 0x00
        
        # Enable bit
        self.En = 0x04
        self.Rw = 0x02
        self.Rs = 0x01
        
        # Initialize display
        self.init_display()
    
    def init_display(self):
        """Initialize the LCD display"""
        try:
            # Wait for LCD to power up
            time.sleep_ms(50)
            
            # Initialize in 4-bit mode
            self.write_4_bits(0x03 << 4)
            time.sleep_ms(5)
            self.write_4_bits(0x03 << 4)
            time.sleep_ms(5)
            self.write_4_bits(0x03 << 4)
            time.sleep_ms(1)
            self.write_4_bits(0x02 << 4)
            
            # Function set: 4-bit mode, 2 lines, 5x8 dots
            self.write_command(self.LCD_FUNCTIONSET | self.LCD_4BITMODE | 
                             self.LCD_2LINE | self.LCD_5x8DOTS)
            
            # Display control: display on, cursor off, blink off
            self.write_command(self.LCD_DISPLAYCONTROL | self.LCD_DISPLAYON | 
                             self.LCD_CURSOROFF | self.LCD_BLINKOFF)
            
            # Clear display
            self.clear()
            
            # Entry mode: left to right, no shift
            self.write_command(self.LCD_ENTRYMODESET | self.LCD_ENTRYLEFT | 
                             self.LCD_ENTRYSHIFTDECREMENT)
            
        except Exception as e:
            print(f"LCD initialization error: {e}")
    
    def write_4_bits(self, value):
        """Write 4 bits to the LCD"""
        try:
            # Add backlight bit
            value |= self.LCD_BACKLIGHT
            self.i2c.writeto(self.address, bytes([value]))
            self.pulse_enable(value)
        except Exception as e:
            print(f"LCD write error: {e}")
    
    def pulse_enable(self, value):
        """Pulse the enable bit"""
        try:
            # Enable high
            self.i2c.writeto(self.address, bytes([value | self.En]))
            time.sleep_us(1)
            # Enable low
            self.i2c.writeto(self.address, bytes([value & ~self.En]))
            time.sleep_us(50)
        except Exception as e:
            print(f"LCD pulse error: {e}")
    
    def write_command(self, command):
        """Write a command to the LCD"""
        self.write_4_bits(command & 0xF0)
        self.write_4_bits((command << 4) & 0xF0)
    
    def write_data(self, data):
        """Write data to the LCD"""
        self.write_4_bits(self.Rs | (data & 0xF0))
        self.write_4_bits(self.Rs | ((data << 4) & 0xF0))
    
    def clear(self):
        """Clear the display"""
        self.write_command(self.LCD_CLEARDISPLAY)
        time.sleep_ms(2)
    
    def home(self):
        """Return cursor to home position"""
        self.write_command(self.LCD_RETURNHOME)
        time.sleep_ms(2)
    
    def set_cursor(self, col, row):
        """Set cursor position"""
        row_offsets = [0x00, 0x40, 0x14, 0x54]
        if row < len(row_offsets):
            self.write_command(self.LCD_SETDDRAMADDR | (col + row_offsets[row]))
    
    def print(self, text):
        """Print text to the LCD"""
        for char in str(text):
            self.write_data(ord(char))
    
    def print_line(self, text, line=0):
        """Print text on a specific line"""
        self.set_cursor(0, line)
        # Pad or truncate text to fit line
        text = str(text)[:self.width].ljust(self.width)
        self.print(text)
    
    def display_on(self):
        """Turn display on"""
        self.write_command(self.LCD_DISPLAYCONTROL | self.LCD_DISPLAYON | 
                         self.LCD_CURSOROFF | self.LCD_BLINKOFF)
    
    def display_off(self):
        """Turn display off"""
        self.write_command(self.LCD_DISPLAYCONTROL | self.LCD_DISPLAYOFF | 
                         self.LCD_CURSOROFF | self.LCD_BLINKOFF)
    
    def cursor_on(self):
        """Turn cursor on"""
        self.write_command(self.LCD_DISPLAYCONTROL | self.LCD_DISPLAYON | 
                         self.LCD_CURSORON | self.LCD_BLINKOFF)
    
    def cursor_off(self):
        """Turn cursor off"""
        self.write_command(self.LCD_DISPLAYCONTROL | self.LCD_DISPLAYON | 
                         self.LCD_CURSOROFF | self.LCD_BLINKOFF)
    
    def blink_on(self):
        """Turn cursor blink on"""
        self.write_command(self.LCD_DISPLAYCONTROL | self.LCD_DISPLAYON | 
                         self.LCD_CURSORON | self.LCD_BLINKON)
    
    def blink_off(self):
        """Turn cursor blink off"""
        self.write_command(self.LCD_DISPLAYCONTROL | self.LCD_DISPLAYON | 
                         self.LCD_CURSORON | self.LCD_BLINKOFF)
