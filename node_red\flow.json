[{"id": "escape_room_endpoints", "type": "tab", "label": "ESP32 Escape Room", "disabled": false, "info": "Custom HTTP endpoints with styled interfaces for escape room system"}, {"id": "http_admin_endpoint", "type": "http in", "z": "escape_room_endpoints", "name": "Admin Dashboard", "url": "/admin", "method": "get", "upload": false, "swaggerDoc": "", "x": 160, "y": 80, "wires": [["serve_admin_page"]]}, {"id": "http_team_a_endpoint", "type": "http in", "z": "escape_room_endpoints", "name": "Team A Interface", "url": "/team-a", "method": "get", "upload": false, "swaggerDoc": "", "x": 160, "y": 140, "wires": [["serve_team_a_page"]]}, {"id": "http_team_b_endpoint", "type": "http in", "z": "escape_room_endpoints", "name": "Team B Interface", "url": "/team-b", "method": "get", "upload": false, "swaggerDoc": "", "x": 160, "y": 200, "wires": [["serve_team_b_page"]]}, {"id": "websocket_admin", "type": "websocket in", "z": "escape_room_endpoints", "name": "Admin WebSocket", "server": "websocket_server_admin", "client": "", "x": 170, "y": 300, "wires": [["handle_admin_websocket"]]}, {"id": "websocket_team_a", "type": "websocket in", "z": "escape_room_endpoints", "name": "Team A WebSocket", "server": "websocket_server_team_a", "client": "", "x": 180, "y": 360, "wires": [["handle_team_a_websocket"]]}, {"id": "websocket_team_b", "type": "websocket in", "z": "escape_room_endpoints", "name": "Team B WebSocket", "server": "websocket_server_team_b", "client": "", "x": 180, "y": 420, "wires": [["handle_team_b_websocket"]]}, {"id": "serve_admin_page", "type": "template", "z": "escape_room_endpoints", "name": "Admin HTML Template", "field": "payload", "fieldType": "msg", "format": "html", "syntax": "mustache", "template": "<!DOCTYPE html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>🔧 Escape Room Admin Dashboard</title>\n    <script src=\"https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js\"></script>\n    <style>\n      * {\n        margin: 0;\n        padding: 0;\n        box-sizing: border-box;\n      }\n\n      html,\n      body {\n        height: 100%;\n        overflow: auto;\n        font-family: \"Inter\", -apple-system, BlinkMacSystemFont, \"Segoe UI\",\n          sans-serif;\n      }\n\n      body {\n        background: linear-gradient(\n          135deg,\n          #0f0f23 0%,\n          #1a1a2e 25%,\n          #16213e 50%,\n          #0f3460 75%,\n          #003d82 100%\n        );\n        background-attachment: fixed;\n        color: #ffffff;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .header {\n        background: rgba(0, 0, 0, 0.5);\n        backdrop-filter: blur(20px);\n        border-bottom: 2px solid rgba(0, 255, 136, 0.5);\n        padding: 12px 20px;\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n        flex-shrink: 0;\n      }\n\n      .header-content {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        max-width: 1400px;\n        margin: 0 auto;\n      }\n\n      .admin-title {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n      }\n\n      .admin-title h1 {\n        font-size: 1.8rem;\n        font-weight: 700;\n        background: linear-gradient(135deg, #00ff88, #00cc6a);\n        -webkit-background-clip: text;\n        -webkit-text-fill-color: transparent;\n        background-clip: text;\n        text-shadow: 0 0 30px rgba(0, 255, 136, 0.3);\n      }\n\n      .admin-badge {\n        background: linear-gradient(135deg, #00ff88, #00cc6a);\n        color: #000;\n        padding: 4px 12px;\n        border-radius: 20px;\n        font-size: 0.75rem;\n        font-weight: 600;\n        text-transform: uppercase;\n        letter-spacing: 1px;\n      }\n\n      .connection-status {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        padding: 6px 12px;\n        border-radius: 20px;\n        font-size: 0.8rem;\n        font-weight: 500;\n      }\n\n      .status-connected {\n        background: rgba(0, 255, 136, 0.2);\n        color: #00ff88;\n        border: 1px solid rgba(0, 255, 136, 0.5);\n      }\n\n      .status-disconnected {\n        background: rgba(255, 107, 107, 0.2);\n        color: #ff6b6b;\n        border: 1px solid rgba(255, 107, 107, 0.5);\n        animation: pulse-red 2s infinite;\n      }\n\n      @keyframes pulse-red {\n        0%,\n        100% {\n          opacity: 1;\n        }\n        50% {\n          opacity: 0.6;\n        }\n      }\n\n      .main-container {\n        flex: 1;\n        display: grid;\n        grid-template-columns: 1fr 1fr;\n        grid-template-rows: auto auto 1fr;\n        gap: 16px;\n        padding: 16px;\n        min-height: 0;\n      }\n\n      .status-card {\n        grid-column: 1 / -1;\n        background: rgba(255, 255, 255, 0.08);\n        backdrop-filter: blur(20px);\n        border: 1px solid rgba(255, 255, 255, 0.2);\n        border-radius: 16px;\n        padding: 20px;\n        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n      }\n\n      .status-header {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        gap: 12px;\n        margin-bottom: 16px;\n      }\n\n      .status-icon {\n        width: 32px;\n        height: 32px;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 1.2rem;\n        background: #00ff88;\n        color: #000;\n      }\n\n      .game-status {\n        font-size: 1.5rem;\n        font-weight: 700;\n        text-transform: uppercase;\n        letter-spacing: 2px;\n        color: #00ff88;\n        text-shadow: 0 0 20px #00ff88;\n      }\n\n      .status-description {\n        text-align: center;\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 0.95rem;\n        line-height: 1.4;\n        margin-bottom: 16px;\n      }\n\n      .team-indicators {\n        display: flex;\n        justify-content: center;\n        gap: 30px;\n        margin-top: 12px;\n      }\n\n      .team-indicator {\n        display: flex;\n        align-items: center;\n        gap: 10px;\n        padding: 8px 16px;\n        background: rgba(255, 255, 255, 0.1);\n        border-radius: 20px;\n        border: 1px solid rgba(255, 255, 255, 0.2);\n      }\n\n      .team-led {\n        width: 12px;\n        height: 12px;\n        border-radius: 50%;\n        background: #666;\n        box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);\n        animation: pulse 2s infinite;\n      }\n\n      .team-led.waiting {\n        background: #ffaa00;\n        box-shadow: 0 0 15px #ffaa00;\n      }\n      .team-led.ready {\n        background: #ffff00;\n        box-shadow: 0 0 15px #ffff00;\n      }\n      .team-led.stage1 {\n        background: #00aaff;\n        box-shadow: 0 0 15px #00aaff;\n      }\n      .team-led.stage2 {\n        background: #aa00ff;\n        box-shadow: 0 0 15px #aa00ff;\n      }\n      .team-led.completed {\n        background: #00ff00;\n        box-shadow: 0 0 15px #00ff00;\n      }\n      .team-led.failed {\n        background: #ff0000;\n        box-shadow: 0 0 15px #ff0000;\n      }\n\n      @keyframes pulse {\n        0%,\n        100% {\n          opacity: 1;\n        }\n        50% {\n          opacity: 0.6;\n        }\n      }\n\n      .card {\n        background: rgba(255, 255, 255, 0.08);\n        backdrop-filter: blur(20px);\n        border: 1px solid rgba(255, 255, 255, 0.2);\n        border-radius: 16px;\n        padding: 20px;\n        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n        display: flex;\n        flex-direction: column;\n        transition: all 0.3s ease;\n        overflow: hidden;\n        min-height: 400px;\n      }\n\n      .card:hover {\n        transform: translateY(-2px);\n        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);\n        border-color: rgba(0, 255, 136, 0.4);\n      }\n\n      .card-header {\n        display: flex;\n        align-items: center;\n        gap: 10px;\n        margin-bottom: 16px;\n        padding-bottom: 12px;\n        border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n      }\n\n      .card-icon {\n        font-size: 1.2rem;\n      }\n\n      .card-title {\n        font-size: 1.1rem;\n        font-weight: 600;\n        color: #00ff88;\n        text-transform: uppercase;\n        letter-spacing: 1px;\n      }\n\n      .control-section {\n        display: flex;\n        flex-direction: column;\n        gap: 16px;\n        margin-bottom: 16px;\n      }\n\n      .control-buttons {\n        display: flex;\n        gap: 12px;\n        justify-content: center;\n      }\n\n      .btn {\n        padding: 12px 24px;\n        border: none;\n        border-radius: 8px;\n        font-size: 0.9rem;\n        font-weight: 600;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        text-transform: uppercase;\n        letter-spacing: 1px;\n        position: relative;\n        overflow: hidden;\n      }\n\n      .btn::before {\n        content: \"\";\n        position: absolute;\n        top: 0;\n        left: -100%;\n        width: 100%;\n        height: 100%;\n        background: linear-gradient(\n          90deg,\n          transparent,\n          rgba(255, 255, 255, 0.2),\n          transparent\n        );\n        transition: left 0.5s;\n      }\n\n      .btn:hover::before {\n        left: 100%;\n      }\n\n      .btn-start {\n        background: linear-gradient(135deg, #00ff88, #00cc6a);\n        color: #000;\n        box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);\n      }\n\n      .btn-start:hover {\n        transform: translateY(-2px);\n        box-shadow: 0 8px 25px rgba(0, 255, 136, 0.7);\n      }\n\n      .btn-reset {\n        background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n        color: #fff;\n        box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);\n      }\n\n      .btn-reset:hover {\n        transform: translateY(-2px);\n        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.7);\n      }\n\n      .timer-display {\n        text-align: center;\n        font-size: 1.8rem;\n        font-weight: 900;\n        color: #00ff88;\n        font-family: \"JetBrains Mono\", \"Courier New\", monospace;\n        text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);\n        background: linear-gradient(\n          135deg,\n          rgba(0, 255, 136, 0.1),\n          rgba(0, 204, 106, 0.1)\n        );\n        border: 2px solid rgba(0, 255, 136, 0.3);\n        border-radius: 12px;\n        padding: 16px;\n        animation: glow-border 3s ease-in-out infinite;\n      }\n\n      @keyframes glow-border {\n        0%,\n        100% {\n          border-color: rgba(0, 255, 136, 0.3);\n          box-shadow: 0 0 20px rgba(0, 255, 136, 0.1);\n        }\n        50% {\n          border-color: rgba(0, 255, 136, 0.6);\n          box-shadow: 0 0 30px rgba(0, 255, 136, 0.3);\n        }\n      }\n\n      .game-info {\n        display: grid;\n        grid-template-columns: 1fr 1fr;\n        gap: 16px;\n        margin-top: 16px;\n      }\n\n      .info-box {\n        background: rgba(0, 0, 0, 0.2);\n        border: 1px solid rgba(0, 255, 136, 0.3);\n        border-radius: 12px;\n        padding: 16px;\n        text-align: center;\n      }\n\n      .info-label {\n        font-size: 0.85rem;\n        color: rgba(255, 255, 255, 0.7);\n        margin-bottom: 8px;\n        text-transform: uppercase;\n        letter-spacing: 1px;\n      }\n\n      .info-value {\n        font-size: 1.5rem;\n        font-weight: 900;\n        color: #00ff88;\n        font-family: \"JetBrains Mono\", monospace;\n        text-shadow: 0 0 15px rgba(0, 255, 136, 0.5);\n      }\n\n      .team-status-section {\n        background: rgba(0, 0, 0, 0.2);\n        border: 1px solid rgba(255, 255, 255, 0.1);\n        border-radius: 12px;\n        padding: 16px;\n        margin-bottom: 16px;\n      }\n\n      .team-status-item {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        padding: 12px;\n        background: rgba(255, 255, 255, 0.05);\n        border-radius: 8px;\n        margin-bottom: 8px;\n        border-left: 3px solid #00ff88;\n      }\n\n      .team-name {\n        font-weight: 600;\n        font-size: 1rem;\n      }\n\n      .team-state-badge {\n        padding: 6px 12px;\n        border-radius: 15px;\n        font-size: 0.8rem;\n        font-weight: 600;\n        text-transform: uppercase;\n        letter-spacing: 0.5px;\n      }\n\n      .state-waiting {\n        background: #ffaa00;\n        color: #000;\n      }\n      .state-ready {\n        background: #ffff00;\n        color: #000;\n      }\n      .state-stage1 {\n        background: #00aaff;\n        color: #fff;\n      }\n      .state-stage2 {\n        background: #aa00ff;\n        color: #fff;\n      }\n      .state-completed {\n        background: #00ff00;\n        color: #000;\n      }\n      .state-failed {\n        background: #ff0000;\n        color: #fff;\n      }\n\n      .logs-area {\n        background: rgba(0, 0, 0, 0.2);\n        border: 1px solid rgba(255, 255, 255, 0.1);\n        border-radius: 12px;\n        padding: 16px;\n        height: 300px;\n        width: 100%;\n        font-family: \"JetBrains Mono\", \"Courier New\", monospace;\n        font-size: 0.85rem;\n        line-height: 1.5;\n        color: #ffffff;\n        box-sizing: border-box;\n        overflow-y: scroll;\n        overflow-x: hidden;\n        scroll-behavior: smooth;\n      }\n\n      .logs-area::-webkit-scrollbar {\n        width: 12px;\n        background: rgba(255, 255, 255, 0.1);\n      }\n\n      .logs-area::-webkit-scrollbar-track {\n        background: rgba(255, 255, 255, 0.2);\n        border-radius: 6px;\n        border: 1px solid rgba(255, 255, 255, 0.3);\n      }\n\n      .logs-area::-webkit-scrollbar-thumb {\n        background: #00ff88;\n        border-radius: 6px;\n        min-height: 40px;\n        border: 2px solid rgba(255, 255, 255, 0.2);\n      }\n\n      .logs-area::-webkit-scrollbar-thumb:hover {\n        background: #00cc6a;\n      }\n\n      .logs-area::-webkit-scrollbar-corner {\n        background: rgba(255, 255, 255, 0.1);\n      }\n\n      .logs-area {\n        scrollbar-width: thick;\n        scrollbar-color: #00ff88 rgba(255, 255, 255, 0.1);\n      }\n\n      .log-entry {\n        margin-bottom: 8px;\n        padding: 8px;\n        background: rgba(255, 255, 255, 0.05);\n        border-radius: 6px;\n        border-left: 3px solid #00ff88;\n        animation: slide-in 0.3s ease;\n        color: #ffffff;\n      }\n\n      @keyframes slide-in {\n        from {\n          opacity: 0;\n          transform: translateX(-20px);\n        }\n        to {\n          opacity: 1;\n          transform: translateX(0);\n        }\n      }\n\n      .log-timestamp {\n        color: #888;\n        font-size: 0.75rem;\n        margin-bottom: 4px;\n      }\n\n      .log-content {\n        color: #fff;\n      }\n\n      .no-logs {\n        text-align: center;\n        color: rgba(255, 255, 255, 0.4);\n        font-style: italic;\n        margin-top: 40px;\n      }\n\n      /* Responsive Design */\n      @media (max-width: 768px) {\n        .main-container {\n          grid-template-columns: 1fr;\n          gap: 12px;\n          padding: 12px;\n        }\n\n        .header-content {\n          flex-direction: column;\n          gap: 8px;\n        }\n\n        .admin-title h1 {\n          font-size: 1.4rem;\n        }\n\n        .card {\n          padding: 16px;\n        }\n\n        .control-buttons {\n          flex-direction: column;\n          align-items: center;\n        }\n\n        .team-indicators {\n          flex-direction: column;\n          gap: 12px;\n        }\n\n        .game-info {\n          grid-template-columns: 1fr;\n        }\n\n        .timer-display {\n          font-size: 1.4rem;\n          padding: 12px;\n        }\n      }\n    </style>\n  </head>\n\n  <body>\n    <header class=\"header\">\n      <div class=\"header-content\">\n        <div class=\"admin-title\">\n          <h1>🔧 ADMIN CONTROL</h1>\n          <div class=\"admin-badge\">Game Master</div>\n        </div>\n        <div class=\"connection-status\" id=\"connectionStatus\">\n          <span>🔄</span>\n          <span>Connecting...</span>\n        </div>\n      </div>\n    </header>\n\n    <main class=\"main-container\">\n      <div class=\"status-card\">\n        <div class=\"status-header\">\n          <div class=\"status-icon\">🎮</div>\n          <div class=\"game-status\" id=\"gameStatus\">SYSTEM READY</div>\n        </div>\n        <div class=\"status-description\" id=\"statusDescription\">\n          Escape Room system initialized. Ready to begin the challenge when both\n          teams are prepared.\n        </div>\n        <div class=\"team-indicators\">\n          <div class=\"team-indicator\">\n            <div class=\"team-led waiting\" id=\"teamALed\"></div>\n            <span id=\"teamAStatus\">Team A: Waiting</span>\n          </div>\n          <div class=\"team-indicator\">\n            <div class=\"team-led waiting\" id=\"teamBLed\"></div>\n            <span id=\"teamBStatus\">Team B: Waiting</span>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"card\">\n        <div class=\"card-header\">\n          <div class=\"card-icon\">🎮</div>\n          <div class=\"card-title\">Game Control</div>\n        </div>\n        <div class=\"control-section\">\n          <div class=\"control-buttons\">\n            <button class=\"btn btn-start\" onclick=\"startGame()\">\n              🚀 Start Game\n            </button>\n            <button class=\"btn btn-reset\" onclick=\"resetGame()\">\n              🔄 Reset Game\n            </button>\n          </div>\n          <div class=\"timer-display\" id=\"gameTimer\">⏱️ Game Not Started</div>\n        </div>\n        <div class=\"game-info\">\n          <div class=\"info-box\">\n            <div class=\"info-label\">Current Code</div>\n            <div class=\"info-value\" id=\"currentCode\">---</div>\n          </div>\n          <div class=\"info-box\">\n            <div class=\"info-label\">Target Weight</div>\n            <div class=\"info-value\" id=\"targetWeight\">---</div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"card\">\n        <div class=\"card-header\">\n          <div class=\"card-icon\">👥</div>\n          <div class=\"card-title\">Team Status</div>\n        </div>\n        <div class=\"team-status-section\">\n          <div class=\"team-status-item\">\n            <div class=\"team-name\">Team A - Code Masters</div>\n            <div class=\"team-state-badge state-waiting\" id=\"teamAState\">\n              Waiting\n            </div>\n          </div>\n          <div class=\"team-status-item\">\n            <div class=\"team-name\">Team B - Code Breakers</div>\n            <div class=\"team-state-badge state-waiting\" id=\"teamBState\">\n              Waiting\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"card\">\n        <div class=\"card-header\">\n          <div class=\"card-icon\">📊</div>\n          <div class=\"card-title\">Game Progress</div>\n        </div>\n        <div class=\"logs-area\" id=\"progressLog\">\n          <div class=\"no-logs\">\n            🎮 Admin dashboard ready. Waiting for game to start...\n          </div>\n        </div>\n      </div>\n\n      <div class=\"card\">\n        <div class=\"card-header\">\n          <div class=\"card-icon\">💬</div>\n          <div class=\"card-title\">Communication Log</div>\n        </div>\n        <div class=\"logs-area\" id=\"communicationLog\">\n          <div class=\"no-logs\">📡 No communications yet...</div>\n        </div>\n      </div>\n    </main>\n\n    <script>\n      let ws;\n      let gameStartTime = null;\n      let timerInterval;\n\n      function connectWebSocket() {\n        const protocol = window.location.protocol === \"https:\" ? \"wss:\" : \"ws:\";\n        const wsUrl = `${protocol}//${window.location.host}/ws/admin`;\n\n        ws = new WebSocket(wsUrl);\n\n        ws.onopen = function () {\n          updateConnectionStatus(true);\n          ws.send(JSON.stringify({ action: \"get_status\" }));\n        };\n\n        ws.onclose = function () {\n          updateConnectionStatus(false);\n          setTimeout(connectWebSocket, 3000);\n        };\n\n        ws.onerror = function () {\n          updateConnectionStatus(false);\n        };\n\n        ws.onmessage = function (event) {\n          try {\n            const data = JSON.parse(event.data);\n            handleWebSocketMessage(data);\n          } catch (e) {\n            console.error(\"Error parsing WebSocket message:\", e);\n          }\n        };\n      }\n\n      function updateConnectionStatus(connected) {\n        const statusEl = document.getElementById(\"connectionStatus\");\n        if (connected) {\n          statusEl.innerHTML = \"<span>🟢</span><span>Connected</span>\";\n          statusEl.className = \"connection-status status-connected\";\n        } else {\n          statusEl.innerHTML = \"<span>🔴</span><span>Disconnected</span>\";\n          statusEl.className = \"connection-status status-disconnected\";\n        }\n      }\n\n      function handleWebSocketMessage(data) {\n        switch (data.action) {\n          case \"status_update\":\n          case \"heartbeat\":\n            updateStatus(data);\n            if (data.game_start_time) {\n              gameStartTime = data.game_start_time;\n              startTimer();\n            }\n            break;\n          case \"team_a_status_update\":\n            updateTeamAStatus(data.status);\n            checkGameStartFromTeamStatus(data.status);\n            break;\n          case \"team_b_status_update\":\n            updateTeamBStatus(data.status);\n            checkGameStartFromTeamStatus(data.status);\n            break;\n          case \"code_generated\":\n            updateCurrentCode(data.code);\n            if (data.progress) updateProgress(data.progress);\n            break;\n          case \"target_weight_set\":\n            updateTargetWeight(data.target_weight);\n            if (data.progress) updateProgress(data.progress);\n            updateGameStatus(\"STAGE 2 - WEIGHT CHALLENGE\");\n            break;\n          case \"game_completed\":\n            handleGameCompleted(data);\n            updateGameStatus(\"GAME COMPLETED\");\n            break;\n          case \"game_failed\":\n            updateGameStatus(\"GAME FAILED\");\n            break;\n        }\n      }\n\n      function updateStatus(data) {\n        if (data.team_a_status) {\n          updateTeamAStatus(data.team_a_status);\n          checkGameStartFromTeamStatus(data.team_a_status);\n        }\n        if (data.team_b_status) {\n          updateTeamBStatus(data.team_b_status);\n          checkGameStartFromTeamStatus(data.team_b_status);\n        }\n\n        if (data.current_code !== undefined && data.current_code !== null) {\n          updateCurrentCode(data.current_code);\n        }\n        if (data.target_weight !== undefined && data.target_weight !== null) {\n          updateTargetWeight(data.target_weight);\n        }\n\n        if (data.communication_log !== undefined) {\n          updateCommunicationLog(data.communication_log);\n        }\n\n        if (data.game_progress !== undefined) {\n          updateProgress(data.game_progress);\n        }\n      }\n\n      function checkGameStartFromTeamStatus(teamStatus) {\n        if (teamStatus && teamStatus.game_state) {\n          if (teamStatus.game_state === \"waiting\") {\n            updateGameStatus(\"SYSTEM READY\");\n          } else if (teamStatus.game_state === \"ready\") {\n            updateGameStatus(\"BOTH TEAMS READY\");\n          } else if (teamStatus.game_state === \"stage1\") {\n            updateGameStatus(\"STAGE 1 - CODE GENERATION\");\n          } else if (teamStatus.game_state === \"stage2\") {\n            updateGameStatus(\"STAGE 2 - WEIGHT CHALLENGE\");\n          } else if (teamStatus.game_state === \"completed\") {\n            updateGameStatus(\"GAME COMPLETED\");\n          } else if (teamStatus.game_state === \"failed\") {\n            updateGameStatus(\"GAME FAILED\");\n          }\n        }\n      }\n\n      function updateGameStatus(status) {\n        const gameStatusEl = document.getElementById(\"gameStatus\");\n        const statusDescEl = document.getElementById(\"statusDescription\");\n\n        gameStatusEl.textContent = status;\n\n        const descriptions = {\n          \"SYSTEM READY\":\n            \"Escape Room system initialized. Ready to begin the challenge when both teams are prepared.\",\n          \"BOTH TEAMS READY\":\n            \"Both teams are ready! Game will start automatically in a few seconds.\",\n          \"STAGE 1 - CODE GENERATION\":\n            \"Stage 1 in progress: Team A is generating and communicating the secret code to Team B.\",\n          \"STAGE 2 - WEIGHT CHALLENGE\":\n            \"Stage 2 in progress: Team B has the target weight, Team A must achieve it using the pressure sensor.\",\n          \"GAME COMPLETED\":\n            \"🎉 Mission accomplished! Both teams successfully escaped the room! 🎉\",\n          \"GAME FAILED\": \"💀 Mission failed. Too many incorrect code attempts.\",\n        };\n\n        statusDescEl.textContent =\n          descriptions[status] || \"Game in progress...\";\n      }\n\n      function updateTeamAStatus(status) {\n        const led = document.getElementById(\"teamALed\");\n        const statusText = document.getElementById(\"teamAStatus\");\n        const stateElement = document.getElementById(\"teamAState\");\n\n        statusText.textContent = `Team A: ${status.game_state || \"Unknown\"}`;\n        stateElement.textContent = status.game_state || \"Unknown\";\n\n        led.className = \"team-led \" + (status.game_state || \"waiting\");\n        stateElement.className =\n          \"team-state-badge state-\" + (status.game_state || \"waiting\");\n      }\n\n      function updateTeamBStatus(status) {\n        const led = document.getElementById(\"teamBLed\");\n        const statusText = document.getElementById(\"teamBStatus\");\n        const stateElement = document.getElementById(\"teamBState\");\n\n        statusText.textContent = `Team B: ${status.game_state || \"Unknown\"}`;\n        stateElement.textContent = status.game_state || \"Unknown\";\n\n        led.className = \"team-led \" + (status.game_state || \"waiting\");\n        stateElement.className =\n          \"team-state-badge state-\" + (status.game_state || \"waiting\");\n      }\n\n      function updateCurrentCode(code) {\n        document.getElementById(\"currentCode\").textContent = code || \"---\";\n      }\n\n      function updateTargetWeight(weight) {\n        if (weight) {\n          const weightKg = Math.floor(weight / 1000);\n          document.getElementById(\"targetWeight\").textContent = `${weightKg}kg`;\n        } else {\n          document.getElementById(\"targetWeight\").textContent = \"--kg\";\n        }\n      }\n\n      function updateProgress(progress) {\n        const progressLog = document.getElementById(\"progressLog\");\n\n        if (!progress || progress.trim() === \"\") {\n          progressLog.innerHTML =\n            '<div class=\"no-logs\">🎮 No game progress yet...</div>';\n          return;\n        }\n\n        const lines = progress.split(\"\\n\").filter((line) => line.trim());\n\n        let html = \"\";\n        lines.forEach((line, index) => {\n          if (line.trim()) {\n            const timestampMatch = line.match(/^\\[([^\\]]+)\\]/);\n            const timestamp = timestampMatch ? timestampMatch[1] : \"\";\n            const content = timestampMatch\n              ? line.substring(timestampMatch[0].length).trim()\n              : line;\n\n            html += `<div class=\"log-content\">${content}</div></div>`;\n          }\n        });\n\n        if (html) {\n          progressLog.innerHTML = html;\n          setTimeout(() => {\n            progressLog.scrollTop = progressLog.scrollHeight;\n          }, 100);\n        } else {\n          progressLog.innerHTML =\n            '<div class=\"no-logs\">🎮 No game progress yet...</div>';\n        }\n      }\n\n      function updateCommunicationLog(communications) {\n        const commLog = document.getElementById(\"communicationLog\");\n\n        if (!communications || communications.length === 0) {\n          commLog.innerHTML =\n            '<div class=\"no-logs\">📡 No communications yet...</div>';\n          return;\n        }\n\n        let html = \"\";\n        communications.slice(-10).forEach((comm, index) => {\n          const time = new Date(comm.timestamp).toLocaleTimeString();\n          html += `\n                    <div class=\"log-entry\">\n                        <div class=\"log-timestamp\">[${time}] ${comm.from} → ${comm.to}</div>\n                        <div class=\"log-content\">\"${comm.message}\"</div>\n                    </div>\n                `;\n        });\n\n        commLog.innerHTML = html;\n        setTimeout(() => {\n          commLog.scrollTop = commLog.scrollHeight;\n        }, 100);\n      }\n\n      function startGame() {\n        if (ws && ws.readyState === WebSocket.OPEN) {\n          ws.send(JSON.stringify({ action: \"start_game\" }));\n          updateGameStatus(\"GAME STARTING\");\n        }\n      }\n\n      function resetGame() {\n        if (ws && ws.readyState === WebSocket.OPEN) {\n          ws.send(JSON.stringify({ action: \"reset_game\" }));\n        }\n\n        gameStartTime = null;\n        stopTimer();\n        document.getElementById(\"gameTimer\").textContent =\n          \"⏱️ Game Not Started\";\n        updateGameStatus(\"SYSTEM READY\");\n\n        document.getElementById(\"currentCode\").textContent = \"---\";\n        document.getElementById(\"targetWeight\").textContent = \"---\";\n        document.getElementById(\"progressLog\").innerHTML =\n          '<div class=\"no-logs\">🎮 Admin dashboard ready. Waiting for game to start...</div>';\n        document.getElementById(\"communicationLog\").innerHTML =\n          '<div class=\"no-logs\">📡 No communications yet...</div>';\n      }\n\n      function startTimer() {\n        if (timerInterval) clearInterval(timerInterval);\n\n        timerInterval = setInterval(() => {\n          if (gameStartTime) {\n            const teamAState = document.getElementById(\"teamAState\");\n            const teamBState = document.getElementById(\"teamBState\");\n            const gameStatus = document.getElementById(\"gameStatus\");\n\n            // Stop timer if game is completed or failed\n            if (\n              gameStatus.textContent === \"GAME COMPLETED\" ||\n              gameStatus.textContent === \"GAME FAILED\" ||\n              teamAState.textContent.toLowerCase() === \"completed\" ||\n              teamBState.textContent.toLowerCase() === \"completed\"\n            ) {\n              stopTimer();\n              return;\n            }\n\n            const elapsed = Date.now() - gameStartTime;\n            const minutes = Math.floor(elapsed / 60000);\n            const seconds = Math.floor((elapsed % 60000) / 1000);\n            document.getElementById(\n              \"gameTimer\"\n            ).textContent = `⏱️ ${minutes}:${seconds\n              .toString()\n              .padStart(2, \"0\")}`;\n          }\n        }, 1000);\n      }\n\n      function stopTimer() {\n        if (timerInterval) {\n          clearInterval(timerInterval);\n          timerInterval = null;\n        }\n      }\n\n      function handleGameCompleted(data) {\n        const completion = data.completion_data;\n        if (completion) {\n          stopTimer();\n          const minutes = Math.floor(completion.total_time / 60);\n          const seconds = Math.floor(completion.total_time % 60);\n          document.getElementById(\n            \"gameTimer\"\n          ).textContent = `🎉 COMPLETED IN ${minutes}:${seconds\n            .toString()\n            .padStart(2, \"0\")}!`;\n          updateProgress(data.progress);\n\n          // Update team status indicators\n          const teamALed = document.getElementById(\"teamALed\");\n          const teamBLed = document.getElementById(\"teamBLed\");\n          const teamAStatus = document.getElementById(\"teamAStatus\");\n          const teamBStatus = document.getElementById(\"teamBStatus\");\n          const teamAState = document.getElementById(\"teamAState\");\n          const teamBState = document.getElementById(\"teamBState\");\n\n          teamALed.className = \"team-led completed\";\n          teamBLed.className = \"team-led completed\";\n          teamAStatus.textContent = \"Team A: Completed\";\n          teamBStatus.textContent = \"Team B: Completed\";\n          teamAState.textContent = \"COMPLETED\";\n          teamBState.textContent = \"COMPLETED\";\n          teamAState.className = \"team-state-badge state-completed\";\n          teamBState.className = \"team-state-badge state-completed\";\n\n          // Show celebration effects\n          document.body.classList.add(\"celebration\");\n          confetti({\n            particleCount: 100,\n            spread: 70,\n            origin: { y: 0.6 },\n          });\n          setTimeout(() => document.body.classList.remove(\"celebration\"), 3000);\n        }\n      }\n\n      window.addEventListener(\"load\", connectWebSocket);\n    </script>\n  </body>\n</html>\n", "output": "str", "x": 390, "y": 80, "wires": [["http_response_admin"]]}, {"id": "serve_team_a_page", "type": "template", "z": "escape_room_endpoints", "name": "Team A HTML Template", "field": "payload", "fieldType": "msg", "format": "html", "syntax": "mustache", "template": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>🔐 Team A - Escape Room Interface</title>\n    <style>\n        * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n        }\n\n        html, body {\n            height: 100%;\n            overflow: hidden;\n            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;\n        }\n\n        body {\n            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #11998e 75%, #38ef7d 100%);\n            background-attachment: fixed;\n            color: #ffffff;\n            display: flex;\n            flex-direction: column;\n        }\n\n        .header {\n            background: rgba(0, 0, 0, 0.5);\n            backdrop-filter: blur(20px);\n            border-bottom: 2px solid rgba(56, 239, 125, 0.5);\n            padding: 12px 20px;\n            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n            flex-shrink: 0;\n        }\n\n        .header-content {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            max-width: 1400px;\n            margin: 0 auto;\n        }\n\n        .team-title {\n            display: flex;\n            align-items: center;\n            gap: 12px;\n        }\n\n        .team-title h1 {\n            font-size: 1.8rem;\n            font-weight: 700;\n            background: linear-gradient(135deg, #38ef7d, #11998e);\n            -webkit-background-clip: text;\n            -webkit-text-fill-color: transparent;\n            background-clip: text;\n            text-shadow: 0 0 30px rgba(56, 239, 125, 0.3);\n        }\n\n        .team-badge {\n            background: linear-gradient(135deg, #38ef7d, #11998e);\n            color: #000;\n            padding: 4px 12px;\n            border-radius: 20px;\n            font-size: 0.75rem;\n            font-weight: 600;\n            text-transform: uppercase;\n            letter-spacing: 1px;\n        }\n\n        .connection-status {\n            display: flex;\n            align-items: center;\n            gap: 8px;\n            padding: 6px 12px;\n            border-radius: 20px;\n            font-size: 0.8rem;\n            font-weight: 500;\n        }\n\n        .status-connected {\n            background: rgba(56, 239, 125, 0.2);\n            color: #38ef7d;\n            border: 1px solid rgba(56, 239, 125, 0.5);\n        }\n\n        .status-disconnected {\n            background: rgba(255, 107, 107, 0.2);\n            color: #ff6b6b;\n            border: 1px solid rgba(255, 107, 107, 0.5);\n            animation: pulse-red 2s infinite;\n        }\n\n        @keyframes pulse-red {\n            0%, 100% { opacity: 1; }\n            50% { opacity: 0.6; }\n        }\n\n        .main-container {\n            flex: 1;\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            grid-template-rows: auto 1fr;\n            gap: 16px;\n            padding: 16px;\n            min-height: 0;\n        }\n\n        .status-card {\n            grid-column: 1 / -1;\n            background: rgba(255, 255, 255, 0.08);\n            backdrop-filter: blur(20px);\n            border: 1px solid rgba(255, 255, 255, 0.2);\n            border-radius: 16px;\n            padding: 20px;\n            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n        }\n\n        .status-header {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            gap: 12px;\n            margin-bottom: 16px;\n        }\n\n        .status-icon {\n            width: 32px;\n            height: 32px;\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            font-size: 1.2rem;\n        }\n\n        .game-state {\n            font-size: 1.5rem;\n            font-weight: 700;\n            text-transform: uppercase;\n            letter-spacing: 2px;\n        }\n\n        .state-waiting .status-icon { background: #ffaa00; color: #000; }\n        .state-waiting .game-state { color: #ffaa00; text-shadow: 0 0 20px #ffaa00; }\n        \n        .state-ready .status-icon { background: #ffff00; color: #000; }\n        .state-ready .game-state { color: #ffff00; text-shadow: 0 0 20px #ffff00; }\n        \n        .state-stage1 .status-icon { background: #00aaff; color: #fff; }\n        .state-stage1 .game-state { color: #00aaff; text-shadow: 0 0 20px #00aaff; }\n        \n        .state-stage2 .status-icon { background: #aa00ff; color: #fff; }\n        .state-stage2 .game-state { color: #aa00ff; text-shadow: 0 0 20px #aa00ff; }\n        \n        .state-completed .status-icon { background: #00ff00; color: #000; }\n        .state-completed .game-state { color: #00ff00; text-shadow: 0 0 20px #00ff00; }\n        \n        .state-failed .status-icon { background: #ff0000; color: #fff; }\n        .state-failed .game-state { color: #ff0000; text-shadow: 0 0 20px #ff0000; }\n\n        .status-description {\n            text-align: center;\n            color: rgba(255, 255, 255, 0.8);\n            font-size: 0.95rem;\n            line-height: 1.4;\n        }\n\n        .card {\n            background: rgba(255, 255, 255, 0.08);\n            backdrop-filter: blur(20px);\n            border: 1px solid rgba(255, 255, 255, 0.2);\n            border-radius: 16px;\n            padding: 20px;\n            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n            display: flex;\n            flex-direction: column;\n            transition: all 0.3s ease;\n            overflow: hidden;\n        }\n\n        .card:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);\n            border-color: rgba(56, 239, 125, 0.4);\n        }\n\n        .card-header {\n            display: flex;\n            align-items: center;\n            gap: 10px;\n            margin-bottom: 16px;\n            padding-bottom: 12px;\n            border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n        }\n\n        .card-icon {\n            font-size: 1.2rem;\n        }\n\n        .card-title {\n            font-size: 1.1rem;\n            font-weight: 600;\n            color: #38ef7d;\n            text-transform: uppercase;\n            letter-spacing: 1px;\n        }\n\n        .code-display {\n            flex: 1;\n            display: flex;\n            flex-direction: column;\n            justify-content: center;\n            align-items: center;\n            text-align: center;\n            background: linear-gradient(135deg, rgba(56, 239, 125, 0.1), rgba(17, 153, 142, 0.1));\n            border: 2px solid rgba(56, 239, 125, 0.3);\n            border-radius: 12px;\n            padding: 24px;\n            animation: glow-border 3s ease-in-out infinite;\n        }\n\n        @keyframes glow-border {\n            0%, 100% { border-color: rgba(56, 239, 125, 0.3); box-shadow: 0 0 20px rgba(56, 239, 125, 0.1); }\n            50% { border-color: rgba(56, 239, 125, 0.6); box-shadow: 0 0 30px rgba(56, 239, 125, 0.3); }\n        }\n\n        .code-label {\n            font-size: 0.9rem;\n            color: rgba(255, 255, 255, 0.7);\n            margin-bottom: 12px;\n            text-transform: uppercase;\n            letter-spacing: 1px;\n        }\n\n        .secret-code {\n            font-size: 3rem;\n            font-weight: 900;\n            color: #38ef7d;\n            font-family: 'JetBrains Mono', 'Courier New', monospace;\n            letter-spacing: 8px;\n            text-shadow: 0 0 30px rgba(56, 239, 125, 0.5);\n            margin-bottom: 12px;\n            animation: pulse-glow 2s ease-in-out infinite;\n        }\n\n        @keyframes pulse-glow {\n            0%, 100% { text-shadow: 0 0 30px rgba(56, 239, 125, 0.5); }\n            50% { text-shadow: 0 0 40px rgba(56, 239, 125, 0.8); }\n        }\n\n        .code-instruction {\n            font-size: 0.85rem;\n            color: #ffaa00;\n            font-weight: 500;\n        }\n\n        .communication-card {\n            display: flex;\n            flex-direction: column;\n        }\n\n        .comm-input-section {\n            background: rgba(0, 0, 0, 0.2);\n            border: 1px solid rgba(56, 239, 125, 0.3);\n            border-radius: 12px;\n            padding: 16px;\n            margin-bottom: 16px;\n        }\n\n        .comm-label {\n            font-size: 0.85rem;\n            color: #38ef7d;\n            font-weight: 600;\n            margin-bottom: 12px;\n            text-transform: uppercase;\n            letter-spacing: 1px;\n        }\n\n        .char-inputs-container {\n            display: flex;\n            align-items: center;\n            gap: 12px;\n            margin-bottom: 12px;\n        }\n\n        .char-inputs {\n            display: flex;\n            gap: 8px;\n        }\n\n        .char-input {\n            width: 48px;\n            height: 48px;\n            background: rgba(255, 255, 255, 0.1);\n            border: 2px solid rgba(56, 239, 125, 0.4);\n            border-radius: 8px;\n            color: #fff;\n            font-size: 1.5rem;\n            font-weight: 700;\n            text-align: center;\n            font-family: 'JetBrains Mono', monospace;\n            text-transform: uppercase;\n            transition: all 0.3s ease;\n        }\n\n        .char-input:focus {\n            outline: none;\n            border-color: #38ef7d;\n            background: rgba(56, 239, 125, 0.1);\n            box-shadow: 0 0 20px rgba(56, 239, 125, 0.3);\n            transform: scale(1.05);\n        }\n\n        .char-input::placeholder {\n            color: rgba(255, 255, 255, 0.3);\n        }\n\n        .char-counter {\n            font-size: 0.8rem;\n            color: #ffaa00;\n            font-weight: 500;\n        }\n\n        .send-button {\n            background: linear-gradient(135deg, #38ef7d, #11998e);\n            color: #000;\n            border: none;\n            border-radius: 8px;\n            padding: 10px 20px;\n            font-size: 0.85rem;\n            font-weight: 600;\n            text-transform: uppercase;\n            letter-spacing: 1px;\n            cursor: pointer;\n            transition: all 0.3s ease;\n            align-self: flex-end;\n        }\n\n        .send-button:hover:not(:disabled) {\n            transform: translateY(-2px);\n            box-shadow: 0 8px 25px rgba(56, 239, 125, 0.4);\n        }\n\n        .send-button:disabled {\n            opacity: 0.5;\n            cursor: not-allowed;\n            background: #666;\n        }\n\n        .messages-area {\n            flex: 1;\n            background: rgba(0, 0, 0, 0.2);\n            border: 1px solid rgba(255, 255, 255, 0.1);\n            border-radius: 12px;\n            padding: 16px;\n            overflow-y: auto;\n            min-height: 0;\n        }\n\n        .messages-area::-webkit-scrollbar {\n            width: 4px;\n        }\n\n        .messages-area::-webkit-scrollbar-track {\n            background: rgba(255, 255, 255, 0.1);\n            border-radius: 2px;\n        }\n\n        .messages-area::-webkit-scrollbar-thumb {\n            background: #38ef7d;\n            border-radius: 2px;\n        }\n\n        .message {\n            background: rgba(17, 153, 142, 0.2);\n            border-left: 3px solid #11998e;\n            border-radius: 8px;\n            padding: 12px;\n            margin-bottom: 12px;\n            animation: slide-in 0.3s ease;\n        }\n\n        @keyframes slide-in {\n            from { opacity: 0; transform: translateX(-20px); }\n            to { opacity: 1; transform: translateX(0); }\n        }\n\n        .message-time {\n            font-size: 0.75rem;\n            color: rgba(255, 255, 255, 0.5);\n            margin-bottom: 4px;\n        }\n\n        .message-content {\n            font-size: 0.9rem;\n            color: #fff;\n            font-weight: 500;\n        }\n\n        .no-messages {\n            text-align: center;\n            color: rgba(255, 255, 255, 0.4);\n            font-style: italic;\n            margin-top: 40px;\n        }\n\n        .hidden {\n            display: none !important;\n        }\n\n        .pulse {\n            animation: pulse-scale 2s ease-in-out infinite;\n        }\n\n        @keyframes pulse-scale {\n            0%, 100% { transform: scale(1); }\n            50% { transform: scale(1.02); }\n        }\n\n        /* Celebration effects */\n        .celebration {\n            animation: celebration-hue 3s ease-in-out;\n        }\n\n        @keyframes celebration-hue {\n            0%, 100% { filter: hue-rotate(0deg); }\n            25% { filter: hue-rotate(90deg); }\n            50% { filter: hue-rotate(180deg); }\n            75% { filter: hue-rotate(270deg); }\n        }\n\n        .confetti {\n            position: fixed;\n            width: 8px;\n            height: 8px;\n            border-radius: 50%;\n            pointer-events: none;\n            z-index: 1000;\n            animation: confetti-fall linear forwards;\n        }\n\n        @keyframes confetti-fall {\n            0% {\n                transform: translateY(-100vh) rotate(0deg);\n                opacity: 1;\n            }\n            100% {\n                transform: translateY(100vh) rotate(720deg);\n                opacity: 0;\n            }\n        }\n\n        /* Responsive Design */\n        @media (max-width: 768px) {\n            .main-container {\n                grid-template-columns: 1fr;\n                gap: 12px;\n                padding: 12px;\n            }\n\n            .header-content {\n                flex-direction: column;\n                gap: 8px;\n            }\n\n            .team-title h1 {\n                font-size: 1.4rem;\n            }\n\n            .card {\n                padding: 16px;\n            }\n\n            .secret-code {\n                font-size: 2rem;\n                letter-spacing: 4px;\n            }\n\n            .char-input {\n                width: 40px;\n                height: 40px;\n                font-size: 1.2rem;\n            }\n\n            .comm-input-section {\n                padding: 12px;\n            }\n\n            .char-inputs-container {\n                flex-direction: column;\n                align-items: stretch;\n                gap: 8px;\n            }\n\n            .char-inputs {\n                justify-content: center;\n            }\n        }\n    </style>\n</head>\n<body>\n    <header class=\"header\">\n        <div class=\"header-content\">\n            <div class=\"team-title\">\n                <h1>🔐 TEAM A</h1>\n                <div class=\"team-badge\">Code Masters</div>\n            </div>\n            <div class=\"connection-status\" id=\"connectionStatus\">\n                <span>🔄</span>\n                <span>Connecting...</span>\n            </div>\n        </div>\n    </header>\n\n    <main class=\"main-container\">\n        <div class=\"status-card\">\n            <div class=\"status-header\" id=\"statusHeader\">\n                <div class=\"status-icon\">⏳</div>\n                <div class=\"game-state\" id=\"gameState\">WAITING</div>\n            </div>\n            <div class=\"status-description\" id=\"statusDescription\">\n                Press your ready button on the ESP32 to begin the escape room challenge...\n            </div>\n        </div>\n\n        <div class=\"card\" id=\"codeCard\">\n            <div class=\"card-header\">\n                <div class=\"card-icon\">🔐</div>\n                <div class=\"card-title\">Secret Code</div>\n            </div>\n            <div class=\"code-display\">\n                <div class=\"code-label\">Your Generated Code</div>\n                <div class=\"secret-code\" id=\"secretCode\">---</div>\n                <div class=\"code-instruction\">Communicate this to Team B!</div>\n            </div>\n        </div>\n\n        <div class=\"card communication-card\">\n            <div class=\"card-header\">\n                <div class=\"card-icon\">💬</div>\n                <div class=\"card-title\">Communication</div>\n            </div>\n            <div class=\"comm-input-section\">\n                <div class=\"comm-label\">Message to Team B</div>\n                <div class=\"char-inputs-container\">\n                    <div class=\"char-inputs\">\n                        <input type=\"text\" class=\"char-input\" id=\"char1\" maxlength=\"1\" placeholder=\"A\">\n                        <input type=\"text\" class=\"char-input\" id=\"char2\" maxlength=\"1\" placeholder=\"1\">\n                    </div>\n                    <div class=\"char-counter\">\n                        <span id=\"charCount\">0</span>/2\n                    </div>\n                </div>\n                <button class=\"send-button\" id=\"sendBtn\" onclick=\"sendMessage()\">\n                    📤 Send Message\n                </button>\n            </div>\n            <div class=\"messages-area\" id=\"messagesArea\">\n                <div class=\"no-messages\">No messages from Team B yet...</div>\n            </div>\n        </div>\n    </main>\n\n    <script>\n        let ws;\n        let currentGameState = 'waiting';\n\n        function connectWebSocket() {\n            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';\n            const wsUrl = `${protocol}//${window.location.host}/ws/team-a`;\n            \n            ws = new WebSocket(wsUrl);\n            \n            ws.onopen = function() {\n                updateConnectionStatus(true);\n                ws.send(JSON.stringify({action: 'get_status'}));\n            };\n            \n            ws.onclose = function() {\n                updateConnectionStatus(false);\n                setTimeout(connectWebSocket, 3000);\n            };\n            \n            ws.onerror = function() {\n                updateConnectionStatus(false);\n            };\n            \n            ws.onmessage = function(event) {\n                try {\n                    const data = JSON.parse(event.data);\n                    handleMessage(data);\n                } catch (e) {\n                    console.error('WebSocket message error:', e);\n                }\n            };\n        }\n\n        function updateConnectionStatus(connected) {\n            const statusEl = document.getElementById('connectionStatus');\n            if (connected) {\n                statusEl.innerHTML = '<span>🟢</span><span>Connected</span>';\n                statusEl.className = 'connection-status status-connected';\n            } else {\n                statusEl.innerHTML = '<span>🔴</span><span>Disconnected</span>';\n                statusEl.className = 'connection-status status-disconnected';\n            }\n        }\n\n        function handleMessage(data) {\n            console.log('Team A received:', data); // Debug log\n            \n            switch(data.action) {\n                case 'status_update':\n                case 'heartbeat':\n                    updateStatus(data);\n                    break;\n                case 'code_generated':\n                    displayCode(data.code);\n                    break;\n                case 'game_completed':\n                    showCelebration();\n                    break;\n                case 'game_failed':\n                    updateGameState({game_state: 'failed'});\n                    break;\n            }\n        }\n\n        function updateStatus(data) {\n            if (data.team_status) {\n                updateGameState(data.team_status);\n            }\n            if (data.current_code) {\n                displayCode(data.current_code);\n            }\n            if (data.messages) {\n                updateMessages(data.messages);\n            }\n        }\n\n        function updateGameState(status) {\n            const gameState = status.game_state || 'waiting';\n            currentGameState = gameState;\n            \n            const stateEl = document.getElementById('gameState');\n            const headerEl = document.getElementById('statusHeader');\n            const descEl = document.getElementById('statusDescription');\n            \n            stateEl.textContent = gameState.toUpperCase();\n            headerEl.className = `status-header state-${gameState}`;\n            \n            const icons = {\n                waiting: '⏳',\n                ready: '🟡',\n                stage1: '🔐',\n                stage2: '⚖️',\n                completed: '🎉',\n                failed: '💀'\n            };\n            \n            headerEl.querySelector('.status-icon').textContent = icons[gameState] || '❓';\n            \n            const descriptions = {\n                waiting: 'Press your ready button on the ESP32 to begin the escape room challenge...',\n                ready: 'Both teams are ready! The game will start in a few seconds...',\n                stage1: 'Generate and share your secret code with Team B through communication!',\n                stage2: 'Team B will tell you the target weight. Use your pressure sensor to achieve it!',\n                completed: '🎉 MISSION ACCOMPLISHED! You successfully escaped! 🎉',\n                failed: '💀 Mission failed. Too many incorrect attempts. Better luck next time!'\n            };\n            \n            descEl.textContent = descriptions[gameState] || 'Unknown game state...';\n            \n            // Show/hide panels based on game state\n            const codeCard = document.getElementById('codeCard');\n            if (gameState === 'stage1') {\n                codeCard.classList.remove('hidden');\n                codeCard.classList.add('pulse');\n            } else {\n                codeCard.classList.add('hidden');\n                codeCard.classList.remove('pulse');\n            }\n        }\n\n        function displayCode(code) {\n            const codeEl = document.getElementById('secretCode');\n            if (code) {\n                codeEl.textContent = code;\n            } else {\n                codeEl.textContent = '---';\n            }\n        }\n\n        function sendMessage() {\n            const char1 = document.getElementById('char1').value.trim();\n            const char2 = document.getElementById('char2').value.trim();\n            const message = char1 + char2;\n            \n            if (message && ws && ws.readyState === WebSocket.OPEN) {\n                ws.send(JSON.stringify({\n                    action: 'send_message',\n                    message: message\n                }));\n                \n                document.getElementById('char1').value = '';\n                document.getElementById('char2').value = '';\n                updateCharCount();\n                \n                const btn = document.getElementById('sendBtn');\n                btn.disabled = true;\n                setTimeout(() => btn.disabled = false, 1000);\n            }\n        }\n\n        function updateCharCount() {\n            const char1 = document.getElementById('char1').value;\n            const char2 = document.getElementById('char2').value;\n            const count = char1.length + char2.length;\n            document.getElementById('charCount').textContent = count;\n            document.getElementById('sendBtn').disabled = count === 0;\n        }\n\n        function setupInputs() {\n            const char1 = document.getElementById('char1');\n            const char2 = document.getElementById('char2');\n            \n            char1.addEventListener('input', function() {\n                this.value = this.value.toUpperCase();\n                updateCharCount();\n                if (this.value.length === 1) char2.focus();\n            });\n            \n            char2.addEventListener('input', function() {\n                this.value = this.value.toUpperCase();\n                updateCharCount();\n            });\n            \n            char2.addEventListener('keydown', function(e) {\n                if (e.key === 'Backspace' && !this.value) char1.focus();\n            });\n            \n            [char1, char2].forEach(input => {\n                input.addEventListener('keypress', function(e) {\n                    if (e.key === 'Enter') sendMessage();\n                });\n            });\n        }\n\n        function updateMessages(messages) {\n            const area = document.getElementById('messagesArea');\n            \n            if (!messages || messages.length === 0) {\n                area.innerHTML = '<div class=\"no-messages\">No messages from Team B yet...</div>';\n                return;\n            }\n\n            let html = '';\n            messages.slice(-5).forEach(msg => {\n                const time = new Date(msg.timestamp).toLocaleTimeString();\n                html += `\n                    <div class=\"message\">\n                        <div class=\"message-time\">[${time}] From Team B:</div>\n                        <div class=\"message-content\">${msg.message}</div>\n                    </div>\n                `;\n            });\n            \n            area.innerHTML = html;\n            area.scrollTop = area.scrollHeight;\n        }\n\n        function showCelebration() {\n            document.body.classList.add('celebration');\n            createConfetti();\n            setTimeout(() => document.body.classList.remove('celebration'), 3000);\n        }\n\n        function createConfetti() {\n            const colors = ['#38ef7d', '#11998e', '#ffaa00', '#ff6b6b', '#4ecdc4'];\n            for (let i = 0; i < 50; i++) {\n                setTimeout(() => {\n                    const confetti = document.createElement('div');\n                    confetti.className = 'confetti';\n                    confetti.style.left = Math.random() * 100 + 'vw';\n                    confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];\n                    confetti.style.animationDuration = (Math.random() * 3 + 2) + 's';\n                    document.body.appendChild(confetti);\n                    setTimeout(() => confetti.remove(), 5000);\n                }, i * 100);\n            }\n        }\n\n        // Initialize\n        window.addEventListener('load', () => {\n            connectWebSocket();\n            setupInputs();\n            updateCharCount();\n            document.getElementById('codeCard').classList.add('hidden');\n        });\n    </script>\n</body>\n</html>", "output": "str", "x": 400, "y": 140, "wires": [["http_response_team_a"]]}, {"id": "serve_team_b_page", "type": "template", "z": "escape_room_endpoints", "name": "Team B HTML Template", "field": "payload", "fieldType": "msg", "format": "html", "syntax": "mustache", "template": "<!DOCTYPE html>\n<html lang=\"en\">\n  <head>\n    <meta charset=\"UTF-8\" />\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n    <title>🎯 Team B - Escape Room Interface</title>\n    <style>\n      * {\n        margin: 0;\n        padding: 0;\n        box-sizing: border-box;\n      }\n\n      html,\n      body {\n        height: 100%;\n        overflow: hidden;\n        font-family: \"Inter\", -apple-system, BlinkMacSystemFont, \"Segoe UI\",\n          sans-serif;\n      }\n\n      body {\n        background: linear-gradient(\n          135deg,\n          #2c1810 0%,\n          #5c2a6b 25%,\n          #764ba2 50%,\n          #667eea 75%,\n          #f093fb 100%\n        );\n        background-attachment: fixed;\n        color: #ffffff;\n        display: flex;\n        flex-direction: column;\n      }\n\n      .header {\n        background: rgba(0, 0, 0, 0.5);\n        backdrop-filter: blur(20px);\n        border-bottom: 2px solid rgba(240, 147, 251, 0.5);\n        padding: 12px 20px;\n        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n        flex-shrink: 0;\n      }\n\n      .header-content {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        max-width: 1400px;\n        margin: 0 auto;\n      }\n\n      .team-title {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n      }\n\n      .team-title h1 {\n        font-size: 1.8rem;\n        font-weight: 700;\n        background: linear-gradient(135deg, #f093fb, #764ba2);\n        -webkit-background-clip: text;\n        -webkit-text-fill-color: transparent;\n        background-clip: text;\n        text-shadow: 0 0 30px rgba(240, 147, 251, 0.3);\n      }\n\n      .team-badge {\n        background: linear-gradient(135deg, #f093fb, #764ba2);\n        color: #fff;\n        padding: 4px 12px;\n        border-radius: 20px;\n        font-size: 0.75rem;\n        font-weight: 600;\n        text-transform: uppercase;\n        letter-spacing: 1px;\n      }\n\n      .connection-status {\n        display: flex;\n        align-items: center;\n        gap: 8px;\n        padding: 6px 12px;\n        border-radius: 20px;\n        font-size: 0.8rem;\n        font-weight: 500;\n      }\n\n      .status-connected {\n        background: rgba(240, 147, 251, 0.2);\n        color: #f093fb;\n        border: 1px solid rgba(240, 147, 251, 0.5);\n      }\n\n      .status-disconnected {\n        background: rgba(255, 107, 107, 0.2);\n        color: #ff6b6b;\n        border: 1px solid rgba(255, 107, 107, 0.5);\n        animation: pulse-red 2s infinite;\n      }\n\n      @keyframes pulse-red {\n        0%,\n        100% {\n          opacity: 1;\n        }\n\n        50% {\n          opacity: 0.6;\n        }\n      }\n\n      .main-container {\n        flex: 1;\n        display: grid;\n        grid-template-columns: 1fr 1fr;\n        grid-template-rows: auto 1fr;\n        gap: 16px;\n        padding: 16px;\n        min-height: 0;\n      }\n\n      .status-card {\n        grid-column: 1 / -1;\n        background: rgba(255, 255, 255, 0.08);\n        backdrop-filter: blur(20px);\n        border: 1px solid rgba(255, 255, 255, 0.2);\n        border-radius: 16px;\n        padding: 20px;\n        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n      }\n\n      .status-header {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        gap: 12px;\n        margin-bottom: 16px;\n      }\n\n      .status-icon {\n        width: 32px;\n        height: 32px;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 1.2rem;\n      }\n\n      .game-state {\n        font-size: 1.5rem;\n        font-weight: 700;\n        text-transform: uppercase;\n        letter-spacing: 2px;\n      }\n\n      .state-waiting .status-icon {\n        background: #ffaa00;\n        color: #000;\n      }\n\n      .state-waiting .game-state {\n        color: #ffaa00;\n        text-shadow: 0 0 20px #ffaa00;\n      }\n\n      .state-ready .status-icon {\n        background: #ffff00;\n        color: #000;\n      }\n\n      .state-ready .game-state {\n        color: #ffff00;\n        text-shadow: 0 0 20px #ffff00;\n      }\n\n      .state-stage1 .status-icon {\n        background: #00aaff;\n        color: #fff;\n      }\n\n      .state-stage1 .game-state {\n        color: #00aaff;\n        text-shadow: 0 0 20px #00aaff;\n      }\n\n      .state-stage2 .status-icon {\n        background: #aa00ff;\n        color: #fff;\n      }\n\n      .state-stage2 .game-state {\n        color: #aa00ff;\n        text-shadow: 0 0 20px #aa00ff;\n      }\n\n      .state-completed .status-icon {\n        background: #00ff00;\n        color: #000;\n      }\n\n      .state-completed .game-state {\n        color: #00ff00;\n        text-shadow: 0 0 20px #00ff00;\n      }\n\n      .state-failed .status-icon {\n        background: #ff0000;\n        color: #fff;\n      }\n\n      .state-failed .game-state {\n        color: #ff0000;\n        text-shadow: 0 0 20px #ff0000;\n      }\n\n      .status-description {\n        text-align: center;\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 0.95rem;\n        line-height: 1.4;\n      }\n\n      .attempts-indicator {\n        display: flex;\n        justify-content: center;\n        margin-top: 12px;\n        gap: 8px;\n      }\n\n      .attempt-dot {\n        width: 12px;\n        height: 12px;\n        border-radius: 50%;\n        background: rgba(255, 255, 255, 0.2);\n        transition: all 0.3s ease;\n      }\n\n      .attempt-dot.used {\n        background: #ff6b6b;\n      }\n\n      .attempt-dot.remaining {\n        background: #ffaa00;\n        box-shadow: 0 0 10px rgba(255, 170, 0, 0.5);\n      }\n\n      .card {\n        background: rgba(255, 255, 255, 0.08);\n        backdrop-filter: blur(20px);\n        border: 1px solid rgba(255, 255, 255, 0.2);\n        border-radius: 16px;\n        padding: 20px;\n        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n        display: flex;\n        flex-direction: column;\n        transition: all 0.3s ease;\n        overflow: hidden;\n      }\n\n      .card:hover {\n        transform: translateY(-2px);\n        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);\n        border-color: rgba(240, 147, 251, 0.4);\n      }\n\n      .card-header {\n        display: flex;\n        align-items: center;\n        gap: 10px;\n        margin-bottom: 16px;\n        padding-bottom: 12px;\n        border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n      }\n\n      .card-icon {\n        font-size: 1.2rem;\n      }\n\n      .card-title {\n        font-size: 1.1rem;\n        font-weight: 600;\n        color: #f093fb;\n        text-transform: uppercase;\n        letter-spacing: 1px;\n      }\n\n      .puzzle-display {\n        flex: 1;\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        text-align: center;\n        background: linear-gradient(\n          135deg,\n          rgba(240, 147, 251, 0.1),\n          rgba(118, 75, 162, 0.1)\n        );\n        border: 2px solid rgba(240, 147, 251, 0.3);\n        border-radius: 12px;\n        padding: 24px;\n        animation: glow-border 3s ease-in-out infinite;\n      }\n\n      @keyframes glow-border {\n        0%,\n        100% {\n          border-color: rgba(240, 147, 251, 0.3);\n          box-shadow: 0 0 20px rgba(240, 147, 251, 0.1);\n        }\n\n        50% {\n          border-color: rgba(240, 147, 251, 0.6);\n          box-shadow: 0 0 30px rgba(240, 147, 251, 0.3);\n        }\n      }\n\n      .puzzle-label {\n        font-size: 0.9rem;\n        color: rgba(255, 255, 255, 0.7);\n        margin-bottom: 12px;\n        text-transform: uppercase;\n        letter-spacing: 1px;\n      }\n\n      .target-value {\n        font-size: 3rem;\n        font-weight: 900;\n        color: #f093fb;\n        font-family: \"JetBrains Mono\", \"Courier New\", monospace;\n        letter-spacing: 8px;\n        text-shadow: 0 0 30px rgba(240, 147, 251, 0.5);\n        margin-bottom: 12px;\n        animation: pulse-glow 2s ease-in-out infinite;\n      }\n\n      @keyframes pulse-glow {\n        0%,\n        100% {\n          text-shadow: 0 0 30px rgba(240, 147, 251, 0.5);\n        }\n\n        50% {\n          text-shadow: 0 0 40px rgba(240, 147, 251, 0.8);\n        }\n      }\n\n      .puzzle-instruction {\n        font-size: 0.85rem;\n        color: #ffaa00;\n        font-weight: 500;\n        line-height: 1.4;\n      }\n\n      .communication-card {\n        display: flex;\n        flex-direction: column;\n      }\n\n      .comm-input-section {\n        background: rgba(0, 0, 0, 0.2);\n        border: 1px solid rgba(240, 147, 251, 0.3);\n        border-radius: 12px;\n        padding: 16px;\n        margin-bottom: 16px;\n      }\n\n      .comm-label {\n        font-size: 0.85rem;\n        color: #f093fb;\n        font-weight: 600;\n        margin-bottom: 12px;\n        text-transform: uppercase;\n        letter-spacing: 1px;\n      }\n\n      .char-inputs-container {\n        display: flex;\n        align-items: center;\n        gap: 12px;\n        margin-bottom: 12px;\n      }\n\n      .char-inputs {\n        display: flex;\n        gap: 8px;\n      }\n\n      .char-input {\n        width: 48px;\n        height: 48px;\n        background: rgba(255, 255, 255, 0.1);\n        border: 2px solid rgba(240, 147, 251, 0.4);\n        border-radius: 8px;\n        color: #fff;\n        font-size: 1.5rem;\n        font-weight: 700;\n        text-align: center;\n        font-family: \"JetBrains Mono\", monospace;\n        text-transform: uppercase;\n        transition: all 0.3s ease;\n      }\n\n      .char-input:focus {\n        outline: none;\n        border-color: #f093fb;\n        background: rgba(240, 147, 251, 0.1);\n        box-shadow: 0 0 20px rgba(240, 147, 251, 0.3);\n        transform: scale(1.05);\n      }\n\n      .char-input::placeholder {\n        color: rgba(255, 255, 255, 0.3);\n      }\n\n      .char-counter {\n        font-size: 0.8rem;\n        color: #ffaa00;\n        font-weight: 500;\n      }\n\n      .send-button {\n        background: linear-gradient(135deg, #f093fb, #764ba2);\n        color: #fff;\n        border: none;\n        border-radius: 8px;\n        padding: 10px 20px;\n        font-size: 0.85rem;\n        font-weight: 600;\n        text-transform: uppercase;\n        letter-spacing: 1px;\n        cursor: pointer;\n        transition: all 0.3s ease;\n        align-self: flex-end;\n      }\n\n      .send-button:hover:not(:disabled) {\n        transform: translateY(-2px);\n        box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);\n      }\n\n      .send-button:disabled {\n        opacity: 0.5;\n        cursor: not-allowed;\n        background: #666;\n      }\n\n      .messages-area {\n        flex: 1;\n        background: rgba(0, 0, 0, 0.2);\n        border: 1px solid rgba(255, 255, 255, 0.1);\n        border-radius: 12px;\n        padding: 16px;\n        overflow-y: auto;\n        min-height: 0;\n      }\n\n      .messages-area::-webkit-scrollbar {\n        width: 4px;\n      }\n\n      .messages-area::-webkit-scrollbar-track {\n        background: rgba(255, 255, 255, 0.1);\n        border-radius: 2px;\n      }\n\n      .messages-area::-webkit-scrollbar-thumb {\n        background: #f093fb;\n        border-radius: 2px;\n      }\n\n      .message {\n        background: rgba(118, 75, 162, 0.2);\n        border-left: 3px solid #764ba2;\n        border-radius: 8px;\n        padding: 12px;\n        margin-bottom: 12px;\n        animation: slide-in 0.3s ease;\n      }\n\n      @keyframes slide-in {\n        from {\n          opacity: 0;\n          transform: translateX(-20px);\n        }\n\n        to {\n          opacity: 1;\n          transform: translateX(0);\n        }\n      }\n\n      .message-time {\n        font-size: 0.75rem;\n        color: rgba(255, 255, 255, 0.5);\n        margin-bottom: 4px;\n      }\n\n      .message-content {\n        font-size: 0.9rem;\n        color: #fff;\n        font-weight: 500;\n      }\n\n      .no-messages {\n        text-align: center;\n        color: rgba(255, 255, 255, 0.4);\n        font-style: italic;\n        margin-top: 40px;\n      }\n\n      .hidden {\n        display: none !important;\n      }\n\n      .pulse {\n        animation: pulse-scale 2s ease-in-out infinite;\n      }\n\n      @keyframes pulse-scale {\n        0%,\n        100% {\n          transform: scale(1);\n        }\n\n        50% {\n          transform: scale(1.02);\n        }\n      }\n\n      /* Celebration effects */\n      .celebration {\n        animation: celebration-hue 3s ease-in-out;\n      }\n\n      @keyframes celebration-hue {\n        0%,\n        100% {\n          filter: hue-rotate(0deg);\n        }\n\n        25% {\n          filter: hue-rotate(90deg);\n        }\n\n        50% {\n          filter: hue-rotate(180deg);\n        }\n\n        75% {\n          filter: hue-rotate(270deg);\n        }\n      }\n\n      .confetti {\n        position: fixed;\n        width: 8px;\n        height: 8px;\n        border-radius: 50%;\n        pointer-events: none;\n        z-index: 1000;\n        animation: confetti-fall linear forwards;\n      }\n\n      @keyframes confetti-fall {\n        0% {\n          transform: translateY(-100vh) rotate(0deg);\n          opacity: 1;\n        }\n\n        100% {\n          transform: translateY(100vh) rotate(720deg);\n          opacity: 0;\n        }\n      }\n\n      /* Responsive Design */\n      @media (max-width: 768px) {\n        .main-container {\n          grid-template-columns: 1fr;\n          gap: 12px;\n          padding: 12px;\n        }\n\n        .header-content {\n          flex-direction: column;\n          gap: 8px;\n        }\n\n        .team-title h1 {\n          font-size: 1.4rem;\n        }\n\n        .card {\n          padding: 16px;\n        }\n\n        .target-value {\n          font-size: 2rem;\n          letter-spacing: 4px;\n        }\n\n        .char-input {\n          width: 40px;\n          height: 40px;\n          font-size: 1.2rem;\n        }\n\n        .comm-input-section {\n          padding: 12px;\n        }\n\n        .char-inputs-container {\n          flex-direction: column;\n          align-items: stretch;\n          gap: 8px;\n        }\n\n        .char-inputs {\n          justify-content: center;\n        }\n      }\n    </style>\n  </head>\n\n  <body>\n    <header class=\"header\">\n      <div class=\"header-content\">\n        <div class=\"team-title\">\n          <h1>🎯 TEAM B</h1>\n          <div class=\"team-badge\">Code Breakers</div>\n        </div>\n        <div class=\"connection-status\" id=\"connectionStatus\">\n          <span>🔄</span>\n          <span>Connecting...</span>\n        </div>\n      </div>\n    </header>\n\n    <main class=\"main-container\">\n      <div class=\"status-card\">\n        <div class=\"status-header\" id=\"statusHeader\">\n          <div class=\"status-icon\">⏳</div>\n          <div class=\"game-state\" id=\"gameState\">WAITING</div>\n        </div>\n        <div class=\"status-description\" id=\"statusDescription\">\n          Press your confirm button on the ESP32 to begin the escape room\n          challenge...\n        </div>\n        <div\n          class=\"attempts-indicator\"\n          id=\"attemptsIndicator\"\n          style=\"display: none\"\n        >\n          <div class=\"attempt-dot remaining\"></div>\n          <div class=\"attempt-dot remaining\"></div>\n          <div class=\"attempt-dot remaining\"></div>\n          <div class=\"attempt-dot remaining\"></div>\n          <div class=\"attempt-dot remaining\"></div>\n        </div>\n      </div>\n\n      <div class=\"card\" id=\"codeCard\">\n        <div class=\"card-header\">\n          <div class=\"card-icon\">🔐</div>\n          <div class=\"card-title\">Code Breaking</div>\n        </div>\n        <div class=\"puzzle-display\">\n          <div class=\"puzzle-label\">Your Mission</div>\n          <div class=\"puzzle-instruction\">\n            🎯 Get the 3-digit code from Team A<br />\n            🔄 Use rotary switches on your ESP32<br />\n            ✅ Press confirm to submit each attempt\n          </div>\n        </div>\n      </div>\n\n      <div class=\"card communication-card\">\n        <div class=\"card-header\">\n          <div class=\"card-icon\">💬</div>\n          <div class=\"card-title\">Communication</div>\n        </div>\n        <div class=\"comm-input-section\">\n          <div class=\"comm-label\">Message to Team A</div>\n          <div class=\"char-inputs-container\">\n            <div class=\"char-inputs\">\n              <input\n                type=\"text\"\n                class=\"char-input\"\n                id=\"char1\"\n                maxlength=\"1\"\n                placeholder=\"5\"\n              />\n              <input\n                type=\"text\"\n                class=\"char-input\"\n                id=\"char2\"\n                maxlength=\"1\"\n                placeholder=\"K\"\n              />\n            </div>\n            <div class=\"char-counter\"><span id=\"charCount\">0</span>/2</div>\n          </div>\n          <button class=\"send-button\" id=\"sendBtn\" onclick=\"sendMessage()\">\n            📤 Send Message\n          </button>\n        </div>\n        <div class=\"messages-area\" id=\"messagesArea\">\n          <div class=\"no-messages\">No messages from Team A yet...</div>\n        </div>\n      </div>\n\n      <div class=\"card\" id=\"weightCard\">\n        <div class=\"card-header\">\n          <div class=\"card-icon\">⚖️</div>\n          <div class=\"card-title\">Weight Target</div>\n        </div>\n        <div class=\"puzzle-display\">\n          <div class=\"puzzle-label\">Target Weight</div>\n          <div class=\"target-value\" id=\"targetWeight\">---.--kg</div>\n          <div class=\"puzzle-instruction\">\n            Tell Team A to achieve this weight!\n          </div>\n        </div>\n      </div>\n    </main>\n\n    <script>\n      let ws;\n      let celebrationShown = false;\n      let currentGameState = \"waiting\";\n      let previousGameState = \"waiting\";\n\n      function connectWebSocket() {\n        const protocol = window.location.protocol === \"https:\" ? \"wss:\" : \"ws:\";\n        const wsUrl = `${protocol}//${window.location.host}/ws/team-b`;\n\n        ws = new WebSocket(wsUrl);\n\n        ws.onopen = function () {\n          updateConnectionStatus(true);\n          ws.send(JSON.stringify({ action: \"get_status\" }));\n        };\n\n        ws.onclose = function () {\n          updateConnectionStatus(false);\n          setTimeout(connectWebSocket, 3000);\n        };\n\n        ws.onerror = function () {\n          updateConnectionStatus(false);\n        };\n\n        ws.onmessage = function (event) {\n          try {\n            const data = JSON.parse(event.data);\n            handleMessage(data);\n          } catch (e) {\n            console.error(\"WebSocket message error:\", e);\n          }\n        };\n      }\n\n      function updateConnectionStatus(connected) {\n        const statusEl = document.getElementById(\"connectionStatus\");\n        if (connected) {\n          statusEl.innerHTML = \"<span>🟢</span><span>Connected</span>\";\n          statusEl.className = \"connection-status status-connected\";\n        } else {\n          statusEl.innerHTML = \"<span>🔴</span><span>Disconnected</span>\";\n          statusEl.className = \"connection-status status-disconnected\";\n        }\n      }\n\n      function handleMessage(data) {\n        console.log(\"Team B received:\", data); // Debug log\n\n        switch (data.action) {\n          case \"status_update\":\n          case \"heartbeat\":\n            updateStatus(data);\n            break;\n          case \"target_weight_received\":\n            displayTargetWeight(data.target_weight);\n            break;\n          case \"game_completed\":\n            showCelebration();\n            break;\n          case \"game_failed\":\n            updateGameState({ game_state: \"failed\" });\n            break;\n        }\n      }\n\n      function updateStatus(data) {\n        if (data.team_status) {\n          updateGameState(data.team_status);\n        }\n        if (data.target_weight) {\n          displayTargetWeight(data.target_weight);\n        }\n        if (data.messages) {\n          updateMessages(data.messages);\n        }\n      }\n\n      function updateGameState(status) {\n        const gameState = status.game_state || \"waiting\";\n        const attemptsRemaining = status.attempts_remaining || 5;\n        previousGameState = currentGameState;\n        currentGameState = gameState;\n\n        const stateEl = document.getElementById(\"gameState\");\n        const headerEl = document.getElementById(\"statusHeader\");\n        const descEl = document.getElementById(\"statusDescription\");\n        const attemptsEl = document.getElementById(\"attemptsIndicator\");\n\n        stateEl.textContent = gameState.toUpperCase();\n        headerEl.className = `status-header state-${gameState}`;\n\n        const icons = {\n          waiting: \"⏳\",\n          ready: \"🟡\",\n          stage1: \"🔐\",\n          stage2: \"⚖️\",\n          completed: \"🎉\",\n          failed: \"💀\",\n        };\n\n        headerEl.querySelector(\".status-icon\").textContent =\n          icons[gameState] || \"❓\";\n\n        const descriptions = {\n          waiting:\n            \"Press your confirm button on the ESP32 to begin the escape room challenge...\",\n          ready:\n            \"Both teams are ready! The game will start in a few seconds...\",\n          stage1:\n            \"Get the secret code from Team A and enter it using your rotary switches!\",\n          stage2:\n            \"You received a target weight. Communicate it to Team A so they can achieve it!\",\n          completed: \"🎉 MISSION ACCOMPLISHED! You successfully escaped! 🎉\",\n          failed:\n            \"💀 Mission failed. No attempts remaining. Better luck next time!\",\n        };\n\n        descEl.textContent = descriptions[gameState] || \"Unknown game state...\";\n\n        // Update attempts indicator\n        if (gameState === \"stage1\") {\n          attemptsEl.style.display = \"flex\";\n          updateAttemptsDisplay(attemptsRemaining);\n        } else {\n          attemptsEl.style.display = \"none\";\n        }\n\n        // Show/hide panels based on game state\n        const codeCard = document.getElementById(\"codeCard\");\n        const weightCard = document.getElementById(\"weightCard\");\n\n        if (gameState === \"stage1\") {\n          codeCard.classList.remove(\"hidden\");\n          codeCard.classList.add(\"pulse\");\n          weightCard.classList.add(\"hidden\");\n        } else if (gameState === \"stage2\") {\n          codeCard.classList.add(\"hidden\");\n          weightCard.classList.remove(\"hidden\");\n          weightCard.classList.add(\"pulse\");\n        } else if (\n          gameState === \"completed\" &&\n          previousGameState !== \"completed\"\n        ) {\n          codeCard.classList.add(\"hidden\");\n          weightCard.classList.add(\"hidden\");\n          showCelebration();\n        } else {\n          codeCard.classList.add(\"hidden\");\n          weightCard.classList.add(\"hidden\");\n        }\n      }\n\n      function updateAttemptsDisplay(remaining) {\n        const dots = document.querySelectorAll(\".attempt-dot\");\n        dots.forEach((dot, index) => {\n          if (index < 5 - remaining) {\n            dot.className = \"attempt-dot used\";\n          } else {\n            dot.className = \"attempt-dot remaining\";\n          }\n        });\n      }\n\n      function displayTargetWeight(weight) {\n        const weightEl = document.getElementById(\"targetWeight\");\n        if (weight) {\n          const weightKg = Math.floor(weight / 1000);\n          weightEl.textContent = `${weightKg}kg`;\n        } else {\n          weightEl.textContent = \"--kg\";\n        }\n      }\n\n      function sendMessage() {\n        const char1 = document.getElementById(\"char1\").value.trim();\n        const char2 = document.getElementById(\"char2\").value.trim();\n        const message = char1 + char2;\n\n        if (message && ws && ws.readyState === WebSocket.OPEN) {\n          ws.send(\n            JSON.stringify({\n              action: \"send_message\",\n              message: message,\n            })\n          );\n\n          document.getElementById(\"char1\").value = \"\";\n          document.getElementById(\"char2\").value = \"\";\n          updateCharCount();\n\n          const btn = document.getElementById(\"sendBtn\");\n          btn.disabled = true;\n          setTimeout(() => (btn.disabled = false), 1000);\n        }\n      }\n\n      function updateCharCount() {\n        const char1 = document.getElementById(\"char1\").value;\n        const char2 = document.getElementById(\"char2\").value;\n        const count = char1.length + char2.length;\n        document.getElementById(\"charCount\").textContent = count;\n        document.getElementById(\"sendBtn\").disabled = count === 0;\n      }\n\n      function setupInputs() {\n        const char1 = document.getElementById(\"char1\");\n        const char2 = document.getElementById(\"char2\");\n\n        char1.addEventListener(\"input\", function () {\n          this.value = this.value.toUpperCase();\n          updateCharCount();\n          if (this.value.length === 1) char2.focus();\n        });\n\n        char2.addEventListener(\"input\", function () {\n          this.value = this.value.toUpperCase();\n          updateCharCount();\n        });\n\n        char2.addEventListener(\"keydown\", function (e) {\n          if (e.key === \"Backspace\" && !this.value) char1.focus();\n        });\n\n        [char1, char2].forEach((input) => {\n          input.addEventListener(\"keypress\", function (e) {\n            if (e.key === \"Enter\") sendMessage();\n          });\n        });\n      }\n\n      function updateMessages(messages) {\n        const area = document.getElementById(\"messagesArea\");\n\n        if (!messages || messages.length === 0) {\n          area.innerHTML =\n            '<div class=\"no-messages\">No messages from Team A yet...</div>';\n          return;\n        }\n\n        let html = \"\";\n        messages.slice(-5).forEach((msg) => {\n          const time = new Date(msg.timestamp).toLocaleTimeString();\n          html += `\n                    <div class=\"message\">\n                        <div class=\"message-time\">[${time}] From Team A:</div>\n                        <div class=\"message-content\">${msg.message}</div>\n                    </div>\n                `;\n        });\n\n        area.innerHTML = html;\n        area.scrollTop = area.scrollHeight;\n      }\n\n      function showCelebration() {\n        if (celebrationShown) return;\n        document.body.classList.add(\"celebration\");\n        createConfetti();\n        celebrationShown = true;\n        setTimeout(() => {\n          document.body.classList.remove(\"celebration\");\n          // Reset the flag after 10 seconds in case the game is restarted\n          setTimeout(() => {\n            celebrationShown = false;\n          }, 10000);\n        }, 3000);\n      }\n\n      function createConfetti() {\n        const colors = [\"#38ef7d\", \"#11998e\", \"#ffaa00\", \"#ff6b6b\", \"#4ecdc4\"];\n        for (let i = 0; i < 50; i++) {\n          setTimeout(() => {\n            const confetti = document.createElement(\"div\");\n            confetti.className = \"confetti\";\n            confetti.style.left = Math.random() * 100 + \"vw\";\n            confetti.style.backgroundColor =\n              colors[Math.floor(Math.random() * colors.length)];\n            confetti.style.animationDuration = Math.random() * 3 + 2 + \"s\";\n            document.body.appendChild(confetti);\n            setTimeout(() => confetti.remove(), 5000);\n          }, i * 100);\n        }\n      }\n\n      // Initialize\n      window.addEventListener(\"load\", () => {\n        connectWebSocket();\n        setupInputs();\n        updateCharCount();\n        document.getElementById(\"codeCard\").classList.add(\"hidden\");\n        document.getElementById(\"weightCard\").classList.add(\"hidden\");\n      });\n    </script>\n  </body>\n</html>\n", "output": "str", "x": 400, "y": 200, "wires": [["http_response_team_b"]]}, {"id": "http_response_admin", "type": "http response", "z": "escape_room_endpoints", "name": "Admin Response", "statusCode": "", "headers": {"Content-Type": "text/html"}, "x": 620, "y": 80, "wires": []}, {"id": "http_response_team_a", "type": "http response", "z": "escape_room_endpoints", "name": "Team A Response", "statusCode": "", "headers": {"Content-Type": "text/html"}, "x": 630, "y": 140, "wires": []}, {"id": "http_response_team_b", "type": "http response", "z": "escape_room_endpoints", "name": "Team B Response", "statusCode": "", "headers": {"Content-Type": "text/html"}, "x": 630, "y": 200, "wires": []}, {"id": "handle_admin_websocket", "type": "function", "z": "escape_room_endpoints", "name": "Handle Admin WebSocket", "func": "// Parse incoming WebSocket message\ntry {\n    var data = JSON.parse(msg.payload);\n    \n    if (data.action === 'start_game') {\n        // Store game start time in global context\n        context.global.set('game_start_time', Date.now());\n        \n        // Send start game command via MQTT\n        var mqttMsg = {\n            topic: 'escape_room/game_control',\n            payload: {\n                action: 'start_game',\n                timestamp: Date.now()\n            }\n        };\n        return [mqttMsg, null];\n    } \n    else if (data.action === 'reset_game') {\n        // Clear all global context\n        context.global.set('team_a_status', null);\n        context.global.set('team_b_status', null);\n        context.global.set('current_code', null);\n        context.global.set('target_weight', null);\n        context.global.set('communication_log', []);\n        context.global.set('game_progress', '');\n        context.global.set('game_start_time', null);\n        \n        // Send reset game command via MQTT\n        var mqttMsg = {\n            topic: 'escape_room/game_control',\n            payload: {\n                action: 'reset_game',\n                timestamp: Date.now()\n            }\n        };\n        \n        // Send status update to websocket\n        var wsMsg = {\n            payload: JSON.stringify({\n                action: 'status_update',\n                team_a_status: { game_state: 'waiting' },\n                team_b_status: { game_state: 'waiting' },\n                current_code: null,\n                target_weight: null,\n                communication_log: [],\n                game_progress: ''\n            })\n        };\n        \n        return [mqttMsg, wsMsg];\n    }\n    else if (data.action === 'get_status') {\n        // Return current game status\n        var response = {\n            action: 'status_update',\n            team_a_status: context.global.get('team_a_status') || {},\n            team_b_status: context.global.get('team_b_status') || {},\n            current_code: context.global.get('current_code'),\n            target_weight: context.global.get('target_weight'),\n            communication_log: context.global.get('communication_log') || [],\n            game_progress: context.global.get('game_progress') || '',\n            game_start_time: context.global.get('game_start_time')\n        };\n        \n        var wsMsg = {\n            payload: JSON.stringify(response)\n        };\n        return [null, wsMsg];\n    }\n} catch (e) {\n    node.warn('Error parsing WebSocket message: ' + e.message);\n}\n\nreturn [null, null];", "outputs": 2, "noerr": 0, "x": 430, "y": 300, "wires": [["mqtt_out_control"], ["websocket_admin_out"]]}, {"id": "handle_team_a_websocket", "type": "function", "z": "escape_room_endpoints", "name": "Handle Team A WebSocket", "func": "// Parse incoming WebSocket message\ntry {\n    var data = JSON.parse(msg.payload);\n    \n    if (data.action === 'send_message') {\n        // Send message to Team B via MQTT\n        var message = data.message.substring(0, 50); // Limit to 50 chars\n        \n        var commData = {\n            from: 'Team A',\n            to: 'Team B',\n            message: message,\n            timestamp: Date.now()\n        };\n        \n        // Store in communication log\n        var commLog = context.global.get('communication_log') || [];\n        commLog.push(commData);\n        if (commLog.length > 20) {\n            commLog.shift();\n        }\n        context.global.set('communication_log', commLog);\n        \n        var mqttMsg = {\n            topic: 'escape_room/communication',\n            payload: commData\n        };\n        return [mqttMsg, null];\n    }\n    else if (data.action === 'get_status') {\n        // Return Team A specific status\n        var response = {\n            action: 'status_update',\n            team_status: context.global.get('team_a_status') || {},\n            current_code: context.global.get('current_code'),\n            messages: (context.global.get('communication_log') || []).filter(msg => msg.to === 'Team A')\n        };\n        \n        var wsMsg = {\n            payload: JSON.stringify(response)\n        };\n        return [null, wsMsg];\n    }\n} catch (e) {\n    node.warn('Error parsing Team A WebSocket message: ' + e.message);\n}\n\nreturn [null, null];", "outputs": 2, "noerr": 0, "x": 440, "y": 360, "wires": [["mqtt_out_control"], ["websocket_team_a_out"]]}, {"id": "handle_team_b_websocket", "type": "function", "z": "escape_room_endpoints", "name": "Handle Team B WebSocket", "func": "// Parse incoming WebSocket message\ntry {\n    var data = JSON.parse(msg.payload);\n    \n    if (data.action === 'send_message') {\n        // Send message to Team A via MQTT\n        var message = data.message.substring(0, 50); // Limit to 50 chars\n        \n        var commData = {\n            from: 'Team B',\n            to: 'Team A',\n            message: message,\n            timestamp: Date.now()\n        };\n        \n        // Store in communication log\n        var commLog = context.global.get('communication_log') || [];\n        commLog.push(commData);\n        if (commLog.length > 20) {\n            commLog.shift();\n        }\n        context.global.set('communication_log', commLog);\n        \n        var mqttMsg = {\n            topic: 'escape_room/communication',\n            payload: commData\n        };\n        return [mqttMsg, null];\n    }\n    else if (data.action === 'get_status') {\n        // Return Team B specific status\n        var response = {\n            action: 'status_update',\n            team_status: context.global.get('team_b_status') || {},\n            target_weight: context.global.get('target_weight'),\n            messages: (context.global.get('communication_log') || []).filter(msg => msg.to === 'Team B')\n        };\n        \n        var wsMsg = {\n            payload: JSON.stringify(response)\n        };\n        return [null, wsMsg];\n    }\n} catch (e) {\n    node.warn('Error parsing Team B WebSocket message: ' + e.message);\n}\n\nreturn [null, null];", "outputs": 2, "noerr": 0, "x": 440, "y": 420, "wires": [["mqtt_out_control"], ["websocket_team_b_out"]]}, {"id": "websocket_admin_out", "type": "websocket out", "z": "escape_room_endpoints", "name": "Admin WebSocket Out", "server": "websocket_server_admin", "client": "", "x": 700, "y": 300, "wires": []}, {"id": "websocket_team_a_out", "type": "websocket out", "z": "escape_room_endpoints", "name": "Team A WebSocket Out", "server": "websocket_server_team_a", "client": "", "x": 710, "y": 360, "wires": []}, {"id": "websocket_team_b_out", "type": "websocket out", "z": "escape_room_endpoints", "name": "Team B WebSocket Out", "server": "websocket_server_team_b", "client": "", "x": 710, "y": 420, "wires": []}, {"id": "mqtt_out_control", "type": "mqtt out", "z": "escape_room_endpoints", "name": "MQTT Control Out", "topic": "", "qos": "2", "retain": "false", "broker": "mqtt_broker_config", "x": 720, "y": 480, "wires": []}, {"id": "mqtt_in_team_a_status", "type": "mqtt in", "z": "escape_room_endpoints", "name": "Team A Status", "topic": "escape_room/team_a/status", "qos": "2", "datatype": "json", "broker": "mqtt_broker_config", "inputs": 0, "x": 140, "y": 540, "wires": [["process_team_a_status"]]}, {"id": "mqtt_in_team_b_status", "type": "mqtt in", "z": "escape_room_endpoints", "name": "Team B Status", "topic": "escape_room/team_b/status", "qos": "2", "datatype": "json", "broker": "mqtt_broker_config", "inputs": 0, "x": 140, "y": 600, "wires": [["process_team_b_status"]]}, {"id": "mqtt_in_code_generated", "type": "mqtt in", "z": "escape_room_endpoints", "name": "Code Generated", "topic": "escape_room/stage1/code_generated", "qos": "2", "datatype": "json", "broker": "mqtt_broker_config", "inputs": 0, "x": 140, "y": 660, "wires": [["process_code_generated"]]}, {"id": "mqtt_in_target_weight", "type": "mqtt in", "z": "escape_room_endpoints", "name": "Target Weight", "topic": "escape_room/stage2/target_weight", "qos": "2", "datatype": "json", "broker": "mqtt_broker_config", "inputs": 0, "x": 140, "y": 720, "wires": [["77f25c32540fbfb4"]]}, {"id": "mqtt_in_game_completed", "type": "mqtt in", "z": "escape_room_endpoints", "name": "Game Completed", "topic": "escape_room/game_completed", "qos": "2", "datatype": "json", "broker": "mqtt_broker_config", "inputs": 0, "x": 140, "y": 780, "wires": [["process_game_completed"]]}, {"id": "process_team_a_status", "type": "function", "z": "escape_room_endpoints", "name": "Process Team A Status", "func": "// Store Team A status and broadcast to websockets\ncontext.global.set('team_a_status', msg.payload);\n\n// Broadcast to admin websocket\nvar adminUpdate = {\n    payload: JSON.stringify({\n        action: 'team_a_status_update',\n        status: msg.payload\n    })\n};\n\n// Broadcast to Team A websocket\nvar teamAUpdate = {\n    payload: JSON.stringify({\n        action: 'status_update',\n        team_status: msg.payload\n    })\n};\n\nreturn [adminUpdate, teamAUpdate];", "outputs": 2, "noerr": 0, "x": 400, "y": 540, "wires": [["websocket_admin_out"], ["websocket_team_a_out"]]}, {"id": "process_team_b_status", "type": "function", "z": "escape_room_endpoints", "name": "Process Team B Status", "func": "// Store Team B status and broadcast to websockets\ncontext.global.set('team_b_status', msg.payload);\n\n// Broadcast to admin websocket\nvar adminUpdate = {\n    payload: JSON.stringify({\n        action: 'team_b_status_update',\n        status: msg.payload\n    })\n};\n\n// Broadcast to Team B websocket\nvar teamBUpdate = {\n    payload: JSON.stringify({\n        action: 'status_update',\n        team_status: msg.payload\n    })\n};\n\nreturn [adminUpdate, teamBUpdate];", "outputs": 2, "noerr": 0, "x": 400, "y": 600, "wires": [["websocket_admin_out"], ["websocket_team_b_out"]]}, {"id": "process_code_generated", "type": "function", "z": "escape_room_endpoints", "name": "Process Code Generated", "func": "// Store code and broadcast to relevant websockets\nvar code = msg.payload.code;\ncontext.global.set('current_code', code);\n\n// Add to game progress log\nvar timestamp = new Date().toLocaleTimeString();\nvar progress = `[${timestamp}] 🔐 Stage 1 Started - Code ${code} generated by Team A\\n`;\nvar currentProgress = context.global.get('game_progress') || '';\ncontext.global.set('game_progress', currentProgress + progress);\n\n// Broadcast to admin websocket\nvar adminUpdate = {\n    payload: JSON.stringify({\n        action: 'code_generated',\n        code: code,\n        progress: currentProgress + progress\n    })\n};\n\n// Broadcast to Team A websocket (they see the code)\nvar teamAUpdate = {\n    payload: JSON.stringify({\n        action: 'code_generated',\n        code: code\n    })\n};\n\nreturn [adminUpdate, teamAUpdate];", "outputs": 2, "noerr": 0, "x": 410, "y": 660, "wires": [["websocket_admin_out"], ["websocket_team_a_out"]]}, {"id": "process_game_completed", "type": "function", "z": "escape_room_endpoints", "name": "Process Game Completed", "func": "// Process game completion and broadcast to all websockets\nvar completion = msg.payload;\nvar timestamp = new Date().toLocaleTimeString();\nvar totalTime = completion.total_time.toFixed(1);\nvar stage1Time = completion.stage1_time.toFixed(1);\nvar stage2Time = completion.stage2_time.toFixed(1);\n\n// Add to game progress log\nvar progress = `[${timestamp}] 🎉 GAME COMPLETED! 🎉\\n`;\nprogress += `Total Time: ${totalTime}s (Stage 1: ${stage1Time}s, Stage 2: ${stage2Time}s)\\n`;\nvar currentProgress = context.global.get('game_progress') || '';\ncontext.global.set('game_progress', currentProgress + progress);\n\n// Broadcast completion to all websockets\nvar completionUpdate = {\n    payload: JSON.stringify({\n        action: 'game_completed',\n        completion_data: completion,\n        progress: currentProgress + progress\n    })\n};\n\nreturn [completionUpdate, completionUpdate, completionUpdate];", "outputs": 3, "noerr": 0, "x": 420, "y": 780, "wires": [["websocket_admin_out"], ["websocket_team_a_out"], ["websocket_team_b_out"]]}, {"id": "status_heartbeat", "type": "inject", "z": "escape_room_endpoints", "name": "Status Heartbeat", "props": [{"p": "payload"}], "repeat": "5", "crontab": "", "once": true, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "str", "x": 180, "y": 840, "wires": [["broadcast_status"]]}, {"id": "broadcast_status", "type": "function", "z": "escape_room_endpoints", "name": "Broadcast Status", "func": "// Broadcast current status to all connected websockets every 5 seconds\nvar adminStatus = {\n    payload: JSON.stringify({\n        action: 'heartbeat',\n        team_a_status: context.global.get('team_a_status') || {},\n        team_b_status: context.global.get('team_b_status') || {},\n        current_code: context.global.get('current_code'),\n        target_weight: context.global.get('target_weight'),\n        communication_log: context.global.get('communication_log') || [],\n        game_progress: context.global.get('game_progress') || '',\n        game_start_time: context.global.get('game_start_time'),\n        timestamp: Date.now()\n    })\n};\n\nvar teamAStatus = {\n    payload: JSON.stringify({\n        action: 'heartbeat',\n        team_status: context.global.get('team_a_status') || {},\n        current_code: context.global.get('current_code'),\n        messages: (context.global.get('communication_log') || []).filter(msg => msg.to === 'Team A'),\n        game_start_time: context.global.get('game_start_time'),\n        timestamp: Date.now()\n    })\n};\n\nvar teamBStatus = {\n    payload: JSON.stringify({\n        action: 'heartbeat',\n        team_status: context.global.get('team_b_status') || {},\n        target_weight: context.global.get('target_weight'),\n        messages: (context.global.get('communication_log') || []).filter(msg => msg.to === 'Team B'),\n        game_start_time: context.global.get('game_start_time'),\n        timestamp: Date.now()\n    })\n};\n\nreturn [adminStatus, teamAStatus, teamBStatus];", "outputs": 3, "noerr": 0, "x": 410, "y": 840, "wires": [["websocket_admin_out"], ["websocket_team_a_out"], ["websocket_team_b_out"]]}, {"id": "handle_reset", "type": "function", "z": "escape_room_endpoints", "name": "<PERSON><PERSON> Reset", "func": "// Clear all game state\ncontext.global.set('game_start_time', null);\ncontext.global.set('team_a_status', null);\ncontext.global.set('team_b_status', null);\ncontext.global.set('current_code', null);\ncontext.global.set('target_weight', null);\ncontext.global.set('communication_log', []);\ncontext.global.set('game_progress', '');\n\n// Broadcast reset to all clients\nconst resetMsg = {\n    action: 'reset_game',\n    status: {\n        game_state: 'waiting',\n        team_a_status: { game_state: 'waiting' },\n        team_b_status: { game_state: 'waiting' },\n        current_code: null,\n        target_weight: null,\n        communication_log: [],\n        game_progress: ''\n    }\n};\n\n// Send reset to all websocket clients\nreturn [{\n    payload: JSON.stringify(resetMsg)\n}, {\n    payload: JSON.stringify(resetMsg)\n}, {\n    payload: JSON.stringify(resetMsg)\n}];", "outputs": 3, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 380, "y": 500, "wires": [["websocket_admin_out"], ["websocket_team_a_out"], ["websocket_team_b_out"]]}, {"id": "websocket_out_admin", "type": "websocket out", "z": "escape_room_endpoints", "name": "Admin WebSocket Out", "server": "websocket_server_admin", "client": "", "x": 620, "y": 480, "wires": []}, {"id": "websocket_out_team_a", "type": "websocket out", "z": "escape_room_endpoints", "name": "Team A WebSocket Out", "server": "websocket_server_team_a", "client": "", "x": 620, "y": 520, "wires": []}, {"id": "websocket_out_team_b", "type": "websocket out", "z": "escape_room_endpoints", "name": "Team B WebSocket Out", "server": "websocket_server_team_b", "client": "", "x": 620, "y": 560, "wires": []}, {"id": "game_start_handler", "type": "function", "z": "escape_room_endpoints", "name": "Handle Game Start", "func": "// Handle game start and teams ready\nif (msg.payload) {\n    // Parse payload if it's a string\n    let payload = msg.payload;\n    try {\n        if (typeof payload === 'string') {\n            payload = JSON.parse(payload);\n        }\n        node.warn(`Received message: ${JSON.stringify(payload)}`);\n    } catch (e) {\n        node.error(`Failed to parse payload: ${e.message}`);\n        return null;\n    }\n    \n    if (payload.action === 'teams_ready') {\n        // Track which team is ready\n        if (payload.team === 'A') {\n            context.global.set('team_a_ready', true);\n            node.warn('Team A is ready');\n        } else if (payload.team === 'B') {\n            context.global.set('team_b_ready', true);\n            node.warn('Team B is ready');\n        }\n        \n        // If both teams are ready, prepare for auto-start\n        if (context.global.get('team_a_ready') && context.global.get('team_b_ready')) {\n            node.warn('Both teams are ready!');\n            // Set ready timestamp\n            context.global.set('teams_ready_time', Date.now());\n            msg.payload = {\n                action: 'teams_ready',\n                timestamp: Date.now()\n            };\n            return msg;\n        }\n    } else if (payload.action === 'start_game') {\n        node.warn(`Start game message received from ${payload.team || 'admin'}`);\n        // Set game start time if not already set\n        if (!context.global.get('game_start_time')) {\n            // Use current time as the start time\n            const startTime = Date.now();\n            context.global.set('game_start_time', startTime);\n            context.global.set('game_completed', false);\n            msg.payload = {\n                action: 'start_game',\n                game_start_time: startTime,\n                timestamp: startTime\n            };\n            \n            node.warn(`Game start time set to: ${new Date(startTime).toISOString()}`);\n            \n            // Reset ready states\n            context.global.set('team_a_ready', false);\n            context.global.set('team_b_ready', false);\n            context.global.set('teams_ready_time', null);\n            \n            // Add to game progress log\n            const progressLog = context.global.get('game_progress') || [];\n            progressLog.push({\n                event: 'game_started',\n                timestamp: startTime,\n                details: `Game started by ${payload.team || 'admin'}`\n            });\n            context.global.set('game_progress', progressLog);\n            return msg;\n        } else {\n            node.warn(`Game already started at: ${new Date(context.global.get('game_start_time')).toISOString()}`);\n        }\n    }\n}\nreturn null;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 420, "y": 220, "wires": [["mqtt_out_control", "websocket_admin_out", "websocket_team_a_out", "websocket_team_b_out"]]}, {"id": "timer_handler", "type": "function", "z": "escape_room_endpoints", "name": "Handle Timer Updates", "func": "// Get game state\nconst gameStartTime = context.global.get('game_start_time');\nconst gameCompleted = context.global.get('game_completed');\n\nnode.warn(`Timer check - Start time: ${gameStartTime ? new Date(gameStartTime).toISOString() : 'not set'}, Completed: ${gameCompleted}`);\n\n// Only update timer if game is running\nif (gameStartTime && !gameCompleted) {\n    const currentTime = Date.now();\n    const elapsedTime = currentTime - gameStartTime;\n    \n    // Calculate minutes and seconds\n    const minutes = Math.floor(elapsedTime / 60000);\n    const seconds = Math.floor((elapsedTime % 60000) / 1000);\n    \n    msg.payload = {\n        action: 'timer_update',\n        elapsed_time: elapsedTime,\n        timestamp: currentTime,\n        formatted_time: `${minutes}:${seconds.toString().padStart(2, '0')}`\n    };\n    \n    node.warn(`Timer update: ${msg.payload.formatted_time}`);\n    return msg;\n} else {\n    node.warn('Timer not running - game not started or already completed');\n}\n\nreturn null;", "outputs": 1, "noerr": 0, "initialize": "", "finalize": "", "libs": [], "x": 420, "y": 280, "wires": [["websocket_admin_out", "websocket_team_a_out", "websocket_team_b_out"]]}, {"id": "timer_trigger", "type": "inject", "z": "escape_room_endpoints", "name": "Timer Update", "props": [{"p": "payload"}], "repeat": "1", "crontab": "", "once": true, "onceDelay": 0.1, "topic": "", "payload": "", "payloadType": "str", "x": 210, "y": 280, "wires": [["timer_handler"]]}, {"id": "mqtt_in_control", "type": "mqtt in", "z": "escape_room_endpoints", "name": "MQTT Game Control", "topic": "escape_room/game_control", "qos": "2", "datatype": "json", "broker": "mqtt_broker_config", "nl": false, "rap": true, "rh": 0, "inputs": 0, "x": 170, "y": 220, "wires": [["game_start_handler"]]}, {"id": "77f25c32540fbfb4", "type": "function", "z": "escape_room_endpoints", "name": "Process Target Weight", "func": "// Store target weight and broadcast to relevant websockets\nvar weight = msg.payload.target_weight;\ncontext.global.set('target_weight', weight);\n\n// Add to game progress log\nvar timestamp = new Date().toLocaleTimeString();\nvar weightKg = Math.floor(weight / 1000);\nvar progress = `[${timestamp}] ⚖️ Stage 2 Started - Target weight: ${weightKg}kg sent to Team B\\n`;\nvar currentProgress = context.global.get('game_progress') || '';\ncontext.global.set('game_progress', currentProgress + progress);\n\n// Broadcast to admin websocket\nvar adminUpdate = {\n    payload: JSON.stringify({\n        action: 'target_weight_set',\n        target_weight: weight,\n        progress: currentProgress + progress\n    })\n};\n\n// Broadcast to Team B websocket (they see the target weight)\nvar teamBUpdate = {\n    payload: JSON.stringify({\n        action: 'target_weight_received',\n        target_weight: weight\n    })\n};\n\nreturn [adminUpdate, teamBUpdate];", "outputs": 2, "noerr": 0, "x": 400, "y": 720, "wires": [["websocket_admin_out"], ["websocket_team_b_out"]]}, {"id": "websocket_server_admin", "type": "websocket-listener", "path": "/ws/admin", "wholemsg": "false"}, {"id": "websocket_server_team_a", "type": "websocket-listener", "path": "/ws/team-a", "wholemsg": "false"}, {"id": "websocket_server_team_b", "type": "websocket-listener", "path": "/ws/team-b", "wholemsg": "false"}, {"id": "mqtt_broker_config", "type": "mqtt-broker", "name": "Escape Room MQTT Broker", "broker": "192.168.178.34", "port": "1883", "clientid": "nodered_escape_room_web", "usetls": false, "compatmode": false, "keepalive": "60", "cleansession": true, "birthTopic": "", "birthQos": "0", "birthPayload": "", "closeTopic": "", "closeQos": "0", "closePayload": "", "willTopic": "", "willQos": "0", "willPayload": ""}]