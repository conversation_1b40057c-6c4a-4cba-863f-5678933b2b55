# main.py for Team B ESP32
# Escape Room Game - Team B (Code Input & Weight Display)

import time
import machine
import network
from umqtt.simple import MQTTClient
import json

# Import custom modules
from hardware.switches import CodeEntry
from utils.logger import log

# Configuration
CLIENT_ID = "team_b_" + str(machine.unique_id())
BROKER = "192.168.178.34"
PORT = 1883

# Team B Pin Configuration
TEAM_B_PINS = {
    'CONFIRM_BUTTON': 13,
    'STATUS_LED': 14,
    'BUZZER': 7,
    'ROTARY_SWITCH_HUNDREDS': 6,
    'ROTARY_SWITCH_TENS': 8,
    'ROTARY_SWITCH_ONES': 9,
    'LCD_SDA': 47,
    'LCD_SCL': 38,
}

class EscapeRoomTeamB:
    def __init__(self):
        """Initialize Team B escape room system"""
        log("Initializing Team B Escape Room System", "INFO")
        
        # Hardware initialization
        self.init_hardware()
        
        # Game state
        self.game_state = "waiting"  # waiting, ready, stage1, stage2, completed, failed
        self.ready = False
        self.attempts_remaining = 5
        self.current_input = [0, 0, 0]
        self.target_weight = None
        self.stage1_completed = False
        self.stage2_completed = False
        
        # MQTT
        self.mqtt_client = None
        self.connect_mqtt()
        
        # Display
        self.init_display()
        
        # Auto-start timer
        self.auto_start_time = 0
        
        # Test mode handling (when MQTT is not available)
        self.test_mode_start_time = 0
        
        log("Team B system initialized successfully!", "INFO")
    
    def init_hardware(self):
        """Initialize all hardware components"""
        # Code entry system with rotary switches
        self.code_entry = CodeEntry(
            TEAM_B_PINS['ROTARY_SWITCH_HUNDREDS'],
            TEAM_B_PINS['ROTARY_SWITCH_TENS'],
            TEAM_B_PINS['ROTARY_SWITCH_ONES'],
            TEAM_B_PINS['CONFIRM_BUTTON']
        )
        
        # Status LED
        self.status_led = machine.Pin(TEAM_B_PINS['STATUS_LED'], machine.Pin.OUT)
        self.led_state = False
        self.led_last_toggle = 0
        self.led_pattern = "off"
        
        # Buzzer
        self.buzzer = machine.PWM(machine.Pin(TEAM_B_PINS['BUZZER']))
        self.buzzer.duty(0)
        
        # Ready button (using confirm button when not in game)
        self.ready_button_pressed = False
        
        log("Hardware initialized", "INFO")
    
    def init_display(self):
        """Initialize LCD display"""
        try:
            from machine import I2C
            from hardware.lcd import LCD
            
            i2c = I2C(0, sda=machine.Pin(TEAM_B_PINS['LCD_SDA']), 
                     scl=machine.Pin(TEAM_B_PINS['LCD_SCL']), freq=100000)
            self.lcd = LCD(i2c, 0x27, 20, 4)  # 20x4 LCD
            self.lcd.clear()
            
            self.lcd.print("=== ESCAPE ROOM ===")
            self.lcd.set_cursor(0, 1)
            self.lcd.print("Team B")
            self.lcd.set_cursor(0, 2)
            self.lcd.print("Press button when")
            self.lcd.set_cursor(0, 3)
            self.lcd.print("ready to start!")
            
            log("LCD initialized", "INFO")
        except Exception as e:
            log(f"LCD initialization failed: {e}", "ERROR")
            self.lcd = None
    
    def connect_mqtt(self):
        """Connect to MQTT broker with error handling"""
        try:
            self.mqtt_client = MQTTClient(CLIENT_ID, BROKER, PORT)
            self.mqtt_client.set_callback(self.mqtt_callback)
            self.mqtt_client.connect()
            
            # Subscribe to relevant topics
            self.mqtt_client.subscribe(b"escape_room/game_control")
            self.mqtt_client.subscribe(b"escape_room/team_a/ready")
            self.mqtt_client.subscribe(b"escape_room/stage1/code_generated")
            self.mqtt_client.subscribe(b"escape_room/stage2/target_weight")
            self.mqtt_client.subscribe(b"escape_room/game_failed")
            self.mqtt_client.subscribe(b"escape_room/game_completed")  # Add subscription to game completion
            
            log("Connected to MQTT broker", "INFO")
            return True
        except Exception as e:
            log(f"MQTT connection failed: {e}", "ERROR")
            log("Game will work locally without MQTT", "WARNING")
            self.mqtt_client = None
            return False
    
    def mqtt_callback(self, topic, msg):
        """Handle incoming MQTT messages"""
        try:
            topic = topic.decode()
            message = msg.decode()
            log(f"MQTT received: {topic} -> {message}", "DEBUG")
            
            if topic == "escape_room/game_control":
                data = json.loads(message)
                if data.get("action") == "start_game":
                    log("Received game start command from Team A", "INFO")
                    # Only start if not already completed
                    if self.game_state != "completed":
                        self.start_game()
                    else:
                        log("Ignoring start_game - game already completed", "INFO")
                elif data.get("action") == "reset_game":
                    log("Received game reset command", "INFO")
                    self.reset_game()
            
            elif topic == "escape_room/team_a/ready":
                data = json.loads(message)
                if data.get("ready"):
                    log("Team A is ready! Checking if both teams ready", "DEBUG")
                    # Only check ready state if not completed
                    if self.game_state != "completed":
                        self.check_both_teams_ready()
                    else:
                        log("Ignoring ready check - game already completed", "INFO")
            
            elif topic == "escape_room/stage1/code_generated":
                data = json.loads(message)
                # Only process if not completed
                if self.game_state != "completed":
                    log(f"Stage 1 started, code generated by Team A", "INFO")
                    # We don't show the actual code to Team B - they need to get it via Node-RED
                else:
                    log("Ignoring code generation - game already completed", "INFO")
            
            elif topic == "escape_room/stage2/target_weight":
                data = json.loads(message)
                # Only process if not completed
                if self.game_state != "completed":
                    self.target_weight = data.get("target_weight")
                    log(f"Received target weight: {self.target_weight}g", "DEBUG")
                    self.game_state = "stage2"
                    self.stage1_completed = True
                    self.update_display()
                    self.play_sound("success")
                    log(f"Stage 2 started! Target weight: {self.target_weight}g", "INFO")
                else:
                    log("Ignoring target weight - game already completed", "INFO")
            
            elif topic == "escape_room/game_failed":
                data = json.loads(message)
                if data.get("game_failed"):
                    log("Game failure notification received from Team A", "INFO")
                    self.game_state = "failed"
                    self.update_display()
                    self.play_sound("error")
                    log("Game failed - received failure notification from Team A", "INFO")
            
            elif topic == "escape_room/game_completed":
                data = json.loads(message)
                if data.get("game_completed"):
                    log("Game completion notification received!", "INFO")
                    self.handle_stage2_completed()
                
        except Exception as e:
            log(f"MQTT callback error: {e}", "ERROR")
    
    def check_ready_button(self):
        """Check if ready button was pressed (using confirm button in waiting state)"""
        if self.game_state != "waiting" or self.ready:
            return
        
        # Use the code entry system's confirm button for ready
        result = self.code_entry.update()
        
        if result['confirmed'] and not self.ready_button_pressed:
            log("CONFIRM BUTTON PRESSED! Processing ready state", "DEBUG")
            self.ready_button_pressed = True
            self.set_ready()
            self.play_sound("beep")
            log("Button pressed - Team B ready", "DEBUG")
    
    def set_ready(self):
        """Set team B as ready"""
        log(f"set_ready called - current ready state: {self.ready}", "DEBUG")
        
        if self.ready:  # Prevent multiple ready calls
            log("Already ready - ignoring duplicate call", "DEBUG")
            return
            
        self.ready = True
        log("Team B marked as ready", "DEBUG")
        self.update_display()
        
        # Publish ready status
        if self.mqtt_client:
            ready_data = {
                "team": "B",
                "ready": True,
                "timestamp": time.time()
            }
            try:
                self.mqtt_client.publish(b"escape_room/team_b/ready", json.dumps(ready_data))
                log("Published Team B ready status via MQTT", "DEBUG")
            except Exception as e:
                log(f"Failed to publish ready status: {e}", "WARNING")
        else:
            log("No MQTT client - ready status not published", "DEBUG")
        
        # DON'T auto-start here - wait for Team A to be ready too
        # Only set ready state, don't advance to game start
        log("Waiting for Team A to be ready before starting game", "DEBUG")
        
        # For testing without MQTT - simulate both teams ready after 3 seconds
        if not self.mqtt_client:
            log("No MQTT - will auto-start game in 3 seconds for testing", "INFO")
            self.test_mode_start_time = time.time()
        
        log("Team B is ready!", "INFO")
    
    def check_both_teams_ready(self):
        """Check if both teams are ready to start"""
        log(f"check_both_teams_ready called - Team B ready: {self.ready}, game_state: {self.game_state}", "DEBUG")
        
        if self.ready and self.game_state == "waiting":
            log("Both teams are ready! Initiating game start sequence", "INFO")
            self.game_state = "ready"
            self.update_display()
            log("Both teams ready! Starting game in 3 seconds...", "INFO")
            
            # Publish teams ready status
            if self.mqtt_client:
                ready_data = {
                    "action": "teams_ready",
                    "team": "B",
                    "timestamp": int(time.time() * 1000)
                }
                log(f"Publishing teams ready message: {ready_data}", "DEBUG")
                self.mqtt_client.publish(b"escape_room/game_control", json.dumps(ready_data))
            
            # Auto-start the game after 3 seconds
            self.auto_start_time = time.time() + 3
            log(f"Auto-start timer set for: {self.auto_start_time}", "DEBUG")
        else:
            log(f"Not starting yet - Team B ready: {self.ready}, state: {self.game_state}", "DEBUG")
    
    def start_game(self):
        """Start the escape room game"""
        log(f"Team B start_game called, current state: {self.game_state}", "DEBUG")
        
        if self.game_state not in ["ready", "waiting"]:
            log(f"Cannot start game from state: {self.game_state}", "WARNING")
            return
        
        self.game_state = "stage1"
        self.attempts_remaining = 5
        
        # Reset code entry
        self.code_entry.reset()
        
        # Publish game start
        if self.mqtt_client:
            start_data = {
                "action": "start_game",
                "team": "B",
                "game_start_time": int(time.time() * 1000),  # Convert to milliseconds timestamp
                "timestamp": int(time.time() * 1000)
            }
            log(f"Publishing game start message: {start_data}", "DEBUG")
            self.mqtt_client.publish(b"escape_room/game_control", json.dumps(start_data))
        
        # Update display
        self.update_display()
        
        self.play_sound("success")
        log("Team B: Game started! Waiting for code from Team A...", "INFO")
    
    def check_code_input(self):
        """Check rotary switch code input"""
        if self.game_state != "stage1":
            return
        
        result = self.code_entry.update()
        
        # Update current input display
        if result['code_changed']:
            self.current_input = [
                result['current_code'] // 100 % 10,
                result['current_code'] // 10 % 10,
                result['current_code'] % 10
            ]
            self.update_display()
        
        # Check if code was confirmed
        if result['confirmed']:
            attempted_code = result['confirmed_code']
            self.attempts_remaining -= 1
            
            log(f"Code attempt: {attempted_code:03d}, attempts remaining: {self.attempts_remaining}", "INFO")
            
            # Publish code attempt
            if self.mqtt_client:
                attempt_data = {
                    "code": attempted_code,
                    "attempt": 6 - self.attempts_remaining,  # Attempt number (1-5)
                    "attempts_remaining": self.attempts_remaining,
                    "timestamp": time.time()
                }
                self.mqtt_client.publish(b"escape_room/team_b/code_attempt", json.dumps(attempt_data))
            
            # Don't check for failure here - let Team A decide if code is correct or wrong
            # Team A will send the stage2 message if correct, or we'll get no response if wrong
            self.play_sound("beep")
            self.update_display()
    
    def update_display(self):
        """Update LCD display based on game state - optimized for 20x4 LCD"""
        if not self.lcd:
            return
        
        self.lcd.clear()
        
        if self.game_state == "waiting":
            if self.ready:
                self.lcd.print("Team B: READY")
                self.lcd.set_cursor(0, 1)
                self.lcd.print("Waiting for Team A...")
                self.lcd.set_cursor(0, 2)
                self.lcd.print("")
                self.lcd.set_cursor(0, 3)
                self.lcd.print("Status: Connected")
            else:
                self.lcd.print("=== ESCAPE ROOM ===")
                self.lcd.set_cursor(0, 1)
                self.lcd.print("Team B")
                self.lcd.set_cursor(0, 2)
                self.lcd.print("Press confirm button")
                self.lcd.set_cursor(0, 3)
                self.lcd.print("when ready to start!")
        
        elif self.game_state == "ready":
            self.lcd.print("Both teams ready!")
            self.lcd.set_cursor(0, 1)
            self.lcd.print("Game starting soon...")
            self.lcd.set_cursor(0, 3)
            self.lcd.print("Prepare for puzzles!")
        
        elif self.game_state == "stage1":
            # Show current input and attempts
            current_code = self.code_entry.get_current_code()
            self.lcd.print("=== STAGE 1 ===")
            self.lcd.set_cursor(0, 1)
            self.lcd.print(f"Code input: {current_code:03d}")
            self.lcd.set_cursor(0, 2)
            self.lcd.print(f"Attempts left: {self.attempts_remaining}")
            self.lcd.set_cursor(0, 3)
            self.lcd.print("Use dials + confirm!")
        
        elif self.game_state == "stage2":
            self.lcd.print("=== STAGE 2 ===")
            self.lcd.set_cursor(0, 1)
            self.lcd.print("Weight challenge:")
            self.lcd.set_cursor(0, 2)
            if self.target_weight:
                # Display weight in kg with whole numbers
                weight_kg = self.target_weight // 1000
                self.lcd.print(f"Need: {weight_kg}kg")
                self.lcd.set_cursor(0, 3)
                self.lcd.print("Tell Team A this!")
            else:
                self.lcd.print("Loading target...")
                self.lcd.set_cursor(0, 3)
                self.lcd.print("Please wait...")
        
        elif self.game_state == "completed":
            self.lcd.print("*** ESCAPED! ***")
            self.lcd.set_cursor(0, 1)
            self.lcd.print("Mission accomplished!")
            self.lcd.set_cursor(0, 2)
            self.lcd.print("Both teams worked")
            self.lcd.set_cursor(0, 3)
            self.lcd.print("together perfectly!")
        
        elif self.game_state == "failed":
            self.lcd.print("*** TRAPPED! ***")
            self.lcd.set_cursor(0, 1)
            self.lcd.print("Mission failed!")
            self.lcd.set_cursor(0, 2)
            self.lcd.print("No attempts left.")
            self.lcd.set_cursor(0, 3)
            self.lcd.print("Better luck next time!")
    
    def update_led(self):
        """Update status LED based on game state"""
        current_time = time.ticks_ms()
        
        if self.game_state == "waiting":
            if self.ready:
                self.led_pattern = "slow_blink"
            else:
                self.led_pattern = "off"
        elif self.game_state == "ready":
            self.led_pattern = "fast_blink"
        elif self.game_state in ["stage1", "stage2"]:
            self.led_pattern = "on"
        elif self.game_state == "completed":
            self.led_pattern = "pulse"
        elif self.game_state == "failed":
            self.led_pattern = "off"
        
        # Handle LED patterns
        if self.led_pattern == "off":
            self.status_led.value(0)
        elif self.led_pattern == "on":
            self.status_led.value(1)
        elif self.led_pattern == "slow_blink":
            if time.ticks_diff(current_time, self.led_last_toggle) > 1000:
                self.led_state = not self.led_state
                self.status_led.value(self.led_state)
                self.led_last_toggle = current_time
        elif self.led_pattern == "fast_blink":
            if time.ticks_diff(current_time, self.led_last_toggle) > 200:
                self.led_state = not self.led_state
                self.status_led.value(self.led_state)
                self.led_last_toggle = current_time
        elif self.led_pattern == "pulse":
            if time.ticks_diff(current_time, self.led_last_toggle) > 500:
                self.led_state = not self.led_state
                self.status_led.value(self.led_state)
                self.led_last_toggle = current_time
    
    def play_sound(self, sound_type):
        """Play buzzer sound"""
        try:
            if sound_type == "beep":
                self.buzzer.freq(800)
                self.buzzer.duty(512)
                time.sleep_ms(200)
                self.buzzer.duty(0)
            
            elif sound_type == "success":
                frequencies = [600, 800, 1000]
                for freq in frequencies:
                    self.buzzer.freq(freq)
                    self.buzzer.duty(512)
                    time.sleep_ms(150)
                    self.buzzer.duty(0)
                    time.sleep_ms(50)
            
            elif sound_type == "error":
                for _ in range(3):
                    self.buzzer.freq(300)
                    self.buzzer.duty(512)
                    time.sleep_ms(100)
                    self.buzzer.duty(0)
                    time.sleep_ms(100)
            
            elif sound_type == "completion":
                frequencies = [523, 659, 784, 1047]
                durations = [200, 200, 200, 400]
                for freq, duration in zip(frequencies, durations):
                    self.buzzer.freq(freq)
                    self.buzzer.duty(512)
                    time.sleep_ms(duration)
                    self.buzzer.duty(0)
                    time.sleep_ms(50)
                    
        except Exception as e:
            log(f"Sound error: {e}", "ERROR")
    
    def reset_game(self):
        """Reset game to initial state"""
        try:
            # Reset game state variables
            self.game_state = "waiting"
            self.ready = False
            self.attempts_remaining = 5
            self.current_input = [0, 0, 0]
            self.target_weight = None
            self.stage1_completed = False
            self.stage2_completed = False
            self.ready_button_pressed = False
            
            # Reset code entry system
            if hasattr(self, 'code_entry'):
                self.code_entry.reset()
            
            # Reset MQTT connection
            if hasattr(self, 'network_manager'):
                self.network_manager.reset_connection()
            
            # Update display
            if hasattr(self, 'lcd'):
                self.lcd.clear()
                self.lcd.print("=== ESCAPE ROOM ===")
                self.lcd.set_cursor(0, 1)
                self.lcd.print("Team B")
                self.lcd.set_cursor(0, 2)
                self.lcd.print("Press button when")
                self.lcd.set_cursor(0, 3)
                self.lcd.print("ready to start!")
            
            log("Game reset completed", "INFO")
        except Exception as e:
            log(f"Error during game reset: {e}", "ERROR")
    
    def run(self):
        """Main game loop"""
        log("Starting Team B game loop", "INFO")
        
        while True:
            try:
                # Check MQTT messages
                if self.mqtt_client:
                    self.mqtt_client.check_msg()
                
                # Only process game logic if not completed
                if self.game_state != "completed":
                    # Check ready button (only in waiting state)
                    self.check_ready_button()
                    
                    # Auto-start game when both teams ready
                    if (self.game_state == "ready" and self.auto_start_time > 0 and 
                        time.time() >= self.auto_start_time):
                        self.auto_start_time = 0  # Reset timer
                        self.start_game()
                    
                    # Test mode - auto start game if MQTT not available
                    if (not self.mqtt_client and self.ready and 
                        self.game_state == "waiting" and self.test_mode_start_time > 0):
                        if time.time() - self.test_mode_start_time > 3:
                            log("Test mode: Starting game automatically", "INFO")
                            self.game_state = "ready"
                            self.update_display()
                            time.sleep(2)  # Show "ready" state briefly
                            self.start_game()
                    
                    # Check code input (only in stage1)
                    self.check_code_input()
                
                # Update LED
                self.update_led()
                
                # Publish current status periodically
                current_time = time.time()
                if hasattr(self, 'last_status_update'):
                    if current_time - self.last_status_update > 2:  # Every 2 seconds
                        self.publish_status()
                        self.last_status_update = current_time
                else:
                    self.last_status_update = current_time
                
                time.sleep_ms(50)  # 20Hz update rate
                
            except KeyboardInterrupt:
                log("Game stopped by user", "INFO")
                break
            except Exception as e:
                log(f"Game loop error: {e}", "ERROR")
                time.sleep(1)
    
    def publish_status(self):
        """Publish current status to MQTT"""
        if not self.mqtt_client:
            return
        
        current_code = self.code_entry.get_current_code()
        
        status = {
            "team": "B",
            "game_state": self.game_state,
            "ready": self.ready,
            "current_input": current_code,
            "attempts_remaining": self.attempts_remaining,
            "target_weight": self.target_weight,
            "stage1_completed": self.stage1_completed,
            "stage2_completed": self.stage2_completed,
            "timestamp": time.time()
        }
        
        try:
            self.mqtt_client.publish(b"escape_room/team_b/status", json.dumps(status))
        except Exception as e:
            log(f"Status publish error: {e}", "ERROR")

    def handle_stage2_completed(self):
        """Handle stage 2 completion"""
        try:
            # Update game state
            self.stage2_completed = True
            self.game_state = "completed"
            
            # Update display
            if hasattr(self, 'lcd'):
                self.lcd.clear()
                self.lcd.print("*** ESCAPED! ***")
                self.lcd.set_cursor(0, 1)
                self.lcd.print("Mission accomplished!")
                self.lcd.set_cursor(0, 2)
                self.lcd.print("Both teams worked")
                self.lcd.set_cursor(0, 3)
                self.lcd.print("together perfectly!")
            
            # Publish completion status
            if hasattr(self, 'mqtt_client'):
                completion_data = {
                    "state": "completed",
                    "stage2_completed": True,
                    "stage2_complete_time": time.time(),
                    "timestamp": time.time()
                }
                self.mqtt_client.publish(b"escape_room/team_b/status", json.dumps(completion_data))
            
            # Play completion sound
            self.play_sound("completion")
            
            log("🎉 Stage 2 completed! Game finished!", "INFO")
            
        except Exception as e:
            log(f"Error in handle_stage2_completed: {e}", "ERROR")

# Main execution
if __name__ == "__main__":
    try:
        # Check WiFi connection
        wlan = network.WLAN(network.STA_IF)
        if not wlan.isconnected():
            log("WiFi not connected! Check boot.py", "ERROR")
            raise SystemExit
        
        # Start the game
        game = EscapeRoomTeamB()
        game.run()
        
    except Exception as e:
        log(f"Main execution error: {e}", "ERROR")
        raise
