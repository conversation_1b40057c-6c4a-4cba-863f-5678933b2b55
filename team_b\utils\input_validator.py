# utils/input_validator.py
# Input validation system for escape room game

import json
from utils.logger import log

class InputValidator:
    """Validate all inputs to ensure game integrity and prevent errors"""
    
    @staticmethod
    def validate_code(code):
        """Validate 3-digit code input"""
        try:
            if isinstance(code, str):
                code = int(code)
            
            if not isinstance(code, int):
                return False, "Code must be a number"
            
            if not (100 <= code <= 999):
                return False, "Code must be between 100 and 999"
                
            return True, "Valid code"
            
        except (ValueError, TypeError):
            return False, "Invalid code format"
    
    @staticmethod
    def validate_weight(weight):
        """Validate weight input"""
        try:
            if isinstance(weight, str):
                weight = float(weight)
            
            if not isinstance(weight, (int, float)):
                return False, "Weight must be a number"
            
            if weight < 0:
                return False, "Weight cannot be negative"
                
            if weight > 10000:  # 10kg max
                return False, "Weight exceeds maximum (10kg)"
                
            return True, "Valid weight"
            
        except (Value<PERSON>rror, TypeError):
            return False, "Invalid weight format"
    
    @staticmethod
    def validate_message(message):
        """Validate 2-character communication message"""
        if not isinstance(message, str):
            return False, "Message must be a string"
        
        if len(message) > 2:
            return False, "Message too long (max 2 characters)"
            
        if len(message) == 0:
            return False, "Message cannot be empty"
        
        # Check for valid characters (alphanumeric)
        if not message.replace(' ', '').isalnum():
            return False, "Message contains invalid characters"
            
        return True, "Valid message"
    
    @staticmethod
    def validate_game_state(state):
        """Validate game state transitions"""
        valid_states = ["waiting", "ready", "stage1", "stage2", "completed", "failed"]
        
        if not isinstance(state, str):
            return False, "State must be a string"
            
        if state not in valid_states:
            return False, f"Invalid state. Must be one of: {valid_states}"
            
        return True, "Valid state"
    
    @staticmethod
    def validate_attempts(attempts):
        """Validate attempts remaining"""
        try:
            if isinstance(attempts, str):
                attempts = int(attempts)
                
            if not isinstance(attempts, int):
                return False, "Attempts must be an integer"
                
            if attempts < 0:
                return False, "Attempts cannot be negative"
                
            if attempts > 10:
                return False, "Too many attempts (max 10)"
                
            return True, "Valid attempts"
            
        except (ValueError, TypeError):
            return False, "Invalid attempts format"
    
    @staticmethod
    def validate_mqtt_message(topic, message):
        """Validate MQTT message structure"""
        if not isinstance(topic, str):
            return False, "Topic must be a string"
            
        if not topic.startswith("escape_room/"):
            return False, "Invalid topic prefix"
            
        # Check message size (prevent memory issues)
        if len(str(message)) > 1000:  # 1KB max
            return False, "Message too large"
            
        # Try to parse JSON if it looks like JSON
        if isinstance(message, str) and (message.startswith('{') or message.startswith('[')):
            try:
                json.loads(message)
            except json.JSONDecodeError:
                return False, "Invalid JSON format"
                
        return True, "Valid MQTT message"
    
    @staticmethod
    def validate_team_status(status):
        """Validate team status data structure"""
        if not isinstance(status, dict):
            return False, "Status must be a dictionary"
            
        required_fields = ["team", "game_state", "ready", "timestamp"]
        
        for field in required_fields:
            if field not in status:
                return False, f"Missing required field: {field}"
        
        # Validate individual fields
        if status["team"] not in ["A", "B"]:
            return False, "Team must be 'A' or 'B'"
            
        valid, msg = InputValidator.validate_game_state(status["game_state"])
        if not valid:
            return False, f"Invalid game_state: {msg}"
            
        if not isinstance(status["ready"], bool):
            return False, "Ready must be true or false"
            
        return True, "Valid team status"
    
    @staticmethod
    def sanitize_input(value, input_type="string"):
        """Sanitize input to prevent issues"""
        try:
            if input_type == "string":
                # Remove control characters and limit length
                sanitized = str(value).replace('\x00', '').replace('\n', '').replace('\r', '')
                return sanitized[:100]  # Limit to 100 characters
                
            elif input_type == "integer":
                return max(0, min(int(float(str(value))), 999999))  # Clamp to reasonable range
                
            elif input_type == "float":
                return max(0.0, min(float(str(value)), 999999.0))  # Clamp to reasonable range
                
            elif input_type == "code":
                code = int(float(str(value)))
                return max(100, min(code, 999))  # Clamp to valid code range
                
            else:
                return str(value)[:100]  # Default to string handling
                
        except (ValueError, TypeError):
            log(f"Failed to sanitize input: {value} as {input_type}", "WARNING", "InputValidator")
            return None

class ValidationError(Exception):
    """Custom exception for validation errors"""
    def __init__(self, message, field=None):
        self.message = message
        self.field = field
        super().__init__(self.message)

def validate_and_sanitize(value, validation_type, sanitize_type=None):
    """Convenience function to validate and sanitize in one call"""
    # First validate
    validator_map = {
        'code': InputValidator.validate_code,
        'weight': InputValidator.validate_weight,
        'message': InputValidator.validate_message,
        'game_state': InputValidator.validate_game_state,
        'attempts': InputValidator.validate_attempts,
    }
    
    validator = validator_map.get(validation_type)
    if validator:
        valid, msg = validator(value)
        if not valid:
            raise ValidationError(msg)
    
    # Then sanitize if requested
    if sanitize_type:
        return InputValidator.sanitize_input(value, sanitize_type)
    
    return value 