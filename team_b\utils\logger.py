# utils/logger.py
# Enhanced logging utility for ESP32

import time

class Logger:
    # Log levels
    DEBUG = 10
    INFO = 20
    WARNING = 30
    ERROR = 40
    CRITICAL = 50
    
    LEVEL_NAMES = {
        DEBUG: "DEBUG",
        INFO: "INFO", 
        WARNING: "WARNING",
        ERROR: "ERROR",
        CRITICAL: "CRITICAL"
    }
    
    def __init__(self, level=INFO, max_entries=50):
        self.level = level
        self.max_entries = max_entries
        self.log_buffer = []
        self.error_count = 0
        self.warning_count = 0
        
    def _format_message(self, message, level, component=None):
        """Format log message with timestamp and context"""
        try:
            current_time = time.time()
            timestamp = f"{int(current_time)}"
            level_name = self.LEVEL_NAMES.get(level, "UNKNOWN")
            
            if component:
                log_message = f"[{timestamp}] [{level_name}] [{component}] {message}"
            else:
                log_message = f"[{timestamp}] [{level_name}] {message}"
                
            return log_message
        except Exception:
            # Fallback formatting if timestamp fails
            level_name = self.LEVEL_NAMES.get(level, "UNKNOWN")
            return f"[{level_name}] {message}"
    
    def _add_to_buffer(self, message):
        """Add message to circular buffer"""
        self.log_buffer.append(message)
        if len(self.log_buffer) > self.max_entries:
            self.log_buffer.pop(0)  # Remove oldest entry
    
    def log(self, message, level=INFO, component=None):
        """Log message if level is above threshold"""
        if level >= self.level:
            formatted_message = self._format_message(message, level, component)
            print(formatted_message)
            self._add_to_buffer(formatted_message)
            
            # Track error/warning counts
            if level >= self.ERROR:
                self.error_count += 1
            elif level >= self.WARNING:
                self.warning_count += 1
    
    def debug(self, message, component=None):
        """Log debug message"""
        self.log(message, self.DEBUG, component)
    
    def info(self, message, component=None):
        """Log info message"""
        self.log(message, self.INFO, component)
    
    def warning(self, message, component=None):
        """Log warning message"""
        self.log(message, self.WARNING, component)
    
    def error(self, message, component=None):
        """Log error message"""
        self.log(message, self.ERROR, component)
    
    def critical(self, message, component=None):
        """Log critical message"""
        self.log(message, self.CRITICAL, component)
    
    def get_recent_logs(self, count=10):
        """Get recent log entries"""
        return self.log_buffer[-count:] if self.log_buffer else []
    
    def get_error_summary(self):
        """Get error and warning counts"""
        return {
            'errors': self.error_count,
            'warnings': self.warning_count,
            'total_logs': len(self.log_buffer)
        }
    
    def clear_buffer(self):
        """Clear log buffer and reset counters"""
        self.log_buffer.clear()
        self.error_count = 0
        self.warning_count = 0

# Global logger instance
_logger = Logger()

# Convenience functions for backward compatibility
def log(message, level="INFO", component=None):
    """Simple logging function with timestamp"""
    level_mapping = {
        "DEBUG": Logger.DEBUG,
        "INFO": Logger.INFO,
        "WARNING": Logger.WARNING,
        "ERROR": Logger.ERROR,
        "CRITICAL": Logger.CRITICAL
    }
    log_level = level_mapping.get(level.upper(), Logger.INFO)
    _logger.log(message, log_level, component)

def log_error(message, component=None):
    """Log error message"""
    _logger.error(message, component)

def log_info(message, component=None):
    """Log info message"""
    _logger.info(message, component)

def log_debug(message, component=None):
    """Log debug message"""
    _logger.debug(message, component)

def log_warning(message, component=None):
    """Log warning message"""
    _logger.warning(message, component)

def set_log_level(level):
    """Set global log level"""
    _logger.level = level

def get_logger():
    """Get the global logger instance"""
    return _logger
