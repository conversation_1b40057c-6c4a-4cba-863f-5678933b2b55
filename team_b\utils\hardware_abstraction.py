# utils/hardware_abstraction.py
# Hardware Abstraction Layer for ESP32 Escape Room

from machine import Pin, I2C
from utils.logger import log
from config.hardware_config import HARDWARE_CONFIG, LCD_CONFIG

class HardwareError(Exception):
    """Custom exception for hardware-related errors"""
    pass

class HardwareManager:
    """Manages all hardware components with standardized interfaces"""
    
    def __init__(self):
        self.i2c = None
        self.lcd = None
        self.initialized = False
        self.hardware_info = {}
        
    def initialize(self):
        """Initialize all hardware components"""
        try:
            log("Initializing hardware components...", "INFO", "HardwareManager")
            
            # Initialize I2C
            self._init_i2c()
            
            # Initialize LCD
            self._init_lcd()
            
            # Test hardware
            self._test_hardware()
            
            self.initialized = True
            log("Hardware initialization complete", "INFO", "HardwareManager")
            
        except Exception as e:
            log(f"Hardware initialization failed: {e}", "ERROR", "HardwareManager")
            raise HardwareError(f"Failed to initialize hardware: {e}")
    
    def _init_i2c(self):
        """Initialize I2C bus"""
        try:
            sda_pin = Pin(HARDWARE_CONFIG['I2C']['SDA_PIN'])
            scl_pin = Pin(HARDWARE_CONFIG['I2C']['SCL_PIN'])
            
            self.i2c = I2C(
                HARDWARE_CONFIG['I2C']['BUS_ID'],
                sda=sda_pin,
                scl=scl_pin,
                freq=HARDWARE_CONFIG['I2C']['FREQUENCY']
            )
            
            # Scan for devices
            devices = self.i2c.scan()
            self.hardware_info['i2c_devices'] = devices
            
            log(f"I2C initialized. Found devices: {[hex(d) for d in devices]}", "INFO", "HardwareManager")
            
        except Exception as e:
            raise HardwareError(f"I2C initialization failed: {e}")
    
    def _init_lcd(self):
        """Initialize LCD display with auto-detection"""
        try:
            from hardware.lcd import LCD
            
            # Try to detect LCD address
            lcd_address = self._detect_lcd_address()
            
            if lcd_address is None:
                raise HardwareError("LCD not found on I2C bus")
            
            # Initialize LCD with detected/configured parameters
            self.lcd = LCD(
                self.i2c,
                lcd_address,
                LCD_CONFIG['COLS'],
                LCD_CONFIG['ROWS']
            )
            
            self.hardware_info['lcd'] = {
                'address': hex(lcd_address),
                'cols': LCD_CONFIG['COLS'],
                'rows': LCD_CONFIG['ROWS']
            }
            
            log(f"LCD initialized at address {hex(lcd_address)} ({LCD_CONFIG['COLS']}x{LCD_CONFIG['ROWS']})", 
                "INFO", "HardwareManager")
            
        except Exception as e:
            raise HardwareError(f"LCD initialization failed: {e}")
    
    def _detect_lcd_address(self):
        """Detect LCD I2C address"""
        # Common LCD addresses
        possible_addresses = [0x27, 0x3F, 0x26, 0x20]
        
        for addr in possible_addresses:
            if addr in self.hardware_info.get('i2c_devices', []):
                log(f"LCD detected at address {hex(addr)}", "DEBUG", "HardwareManager")
                return addr
        
        # If not found in scan, try configured address
        if LCD_CONFIG['ADDRESS'] in self.hardware_info.get('i2c_devices', []):
            return LCD_CONFIG['ADDRESS']
        
        return None
    
    def _test_hardware(self):
        """Test hardware components"""
        try:
            # Test LCD
            if self.lcd:
                self.lcd.clear()
                self.lcd.putstr("Hardware Test")
                log("LCD test passed", "DEBUG", "HardwareManager")
                
        except Exception as e:
            log(f"Hardware test failed: {e}", "WARNING", "HardwareManager")
    
    def get_lcd(self):
        """Get LCD instance"""
        if not self.initialized:
            raise HardwareError("Hardware not initialized")
        return self.lcd
    
    def get_i2c(self):
        """Get I2C instance"""
        if not self.initialized:
            raise HardwareError("Hardware not initialized")
        return self.i2c
    
    def get_hardware_info(self):
        """Get hardware information"""
        return self.hardware_info.copy()
    
    def reinitialize_component(self, component):
        """Reinitialize specific component"""
        try:
            if component == 'lcd':
                self._init_lcd()
                log(f"Component {component} reinitialized", "INFO", "HardwareManager")
            elif component == 'i2c':
                self._init_i2c()
                log(f"Component {component} reinitialized", "INFO", "HardwareManager")
            else:
                log(f"Unknown component: {component}", "WARNING", "HardwareManager")
                
        except Exception as e:
            log(f"Failed to reinitialize {component}: {e}", "ERROR", "HardwareManager")
            raise HardwareError(f"Failed to reinitialize {component}: {e}")
    
    def check_hardware_health(self):
        """Check hardware component health"""
        health_status = {
            'overall': True,
            'components': {}
        }
        
        # Check I2C
        try:
            devices = self.i2c.scan()
            health_status['components']['i2c'] = {
                'status': 'ok',
                'devices': len(devices),
                'details': f"Found {len(devices)} devices"
            }
        except Exception as e:
            health_status['components']['i2c'] = {
                'status': 'error',
                'error': str(e)
            }
            health_status['overall'] = False
        
        # Check LCD
        try:
            if self.lcd:
                # Try a simple operation
                self.lcd.move_to(0, 0)
                health_status['components']['lcd'] = {
                    'status': 'ok',
                    'details': f"{LCD_CONFIG['COLS']}x{LCD_CONFIG['ROWS']} display"
                }
            else:
                health_status['components']['lcd'] = {
                    'status': 'error',
                    'error': 'LCD not initialized'
                }
                health_status['overall'] = False
        except Exception as e:
            health_status['components']['lcd'] = {
                'status': 'error',
                'error': str(e)
            }
            health_status['overall'] = False
        
        return health_status
    
    def reset_hardware(self):
        """Reset all hardware components"""
        try:
            log("Resetting hardware components...", "INFO", "HardwareManager")
            
            # Reset LCD
            if self.lcd:
                self.lcd.clear()
            
            # Reinitialize everything
            self.initialize()
            
            log("Hardware reset complete", "INFO", "HardwareManager")
            
        except Exception as e:
            log(f"Hardware reset failed: {e}", "ERROR", "HardwareManager")
            raise HardwareError(f"Hardware reset failed: {e}")

# Global hardware manager instance
_hardware_manager = HardwareManager()

# Convenience functions
def get_hardware_manager():
    """Get the global hardware manager instance"""
    return _hardware_manager

def initialize_hardware():
    """Initialize hardware components"""
    _hardware_manager.initialize()

def get_lcd():
    """Get LCD instance"""
    return _hardware_manager.get_lcd()

def get_i2c():
    """Get I2C instance"""
    return _hardware_manager.get_i2c()

def check_hardware():
    """Check hardware health"""
    return _hardware_manager.check_hardware_health()

def get_hardware_info():
    """Get hardware information"""
    return _hardware_manager.get_hardware_info()

class ComponentManager:
    """Base class for managing hardware components"""
    
    def __init__(self, component_name):
        self.component_name = component_name
        self.initialized = False
        self.last_error = None
        
    def initialize(self):
        """Initialize the component"""
        raise NotImplementedError("Subclasses must implement initialize()")
    
    def check_health(self):
        """Check component health"""
        raise NotImplementedError("Subclasses must implement check_health()")
    
    def reset(self):
        """Reset the component"""
        raise NotImplementedError("Subclasses must implement reset()")
    
    def get_status(self):
        """Get component status"""
        return {
            'name': self.component_name,
            'initialized': self.initialized,
            'last_error': self.last_error
        } 