# ESP32 Escape Room - MQTT Communication Analysis

## Table of Contents
1. [MQTT Overview](#mqtt-overview)
2. [Topic Structure](#topic-structure)
3. [Message Formats](#message-formats)
4. [Communication Flow](#communication-flow)
5. [Real-time Synchronization](#real-time-synchronization)
6. [Error Handling](#error-handling)
7. [Performance Considerations](#performance-considerations)

## MQTT Overview

The escape room system uses MQTT (Message Queuing Telemetry Transport) as its primary communication protocol for real-time synchronization between ESP32 devices and the Node-RED dashboard.

### MQTT Architecture
```
┌──────────────┐         ┌──────────────┐         ┌──────────────┐
│   Team A     │  pub    │    MQTT      │   sub   │   Team B     │
│    ESP32     ├────────►│   Broker     │◄────────┤    ESP32     │
└──────┬───────┘         │192.168.178.34│         └──────┬───────┘
       │                 │  Port: 1883  │                 │
       │ sub             └───────┬──────┘            sub  │
       │                         │ pub/sub                │
       └─────────────────────────┴────────────────────────┘
                                 │
                          ┌──────▼──────┐
                          │  Node-RED   │
                          │  Dashboard  │
                          └─────────────┘
```

### Connection Parameters
```python
# MQTT Configuration
BROKER = "192.168.178.34"
PORT = 1883
CLIENT_ID = "team_x_" + str(machine.unique_id())  # Unique per device

# Connection setup
mqtt_client = MQTTClient(CLIENT_ID, BROKER, PORT)
mqtt_client.set_callback(mqtt_callback)
mqtt_client.connect()
```

## Topic Structure

The MQTT topic hierarchy is designed for logical separation and efficient message routing:

### Topic Hierarchy
```
escape_room/
├── game_control           # System-wide game commands
├── team_a/
│   ├── ready             # Team A ready status
│   └── status            # Team A periodic updates
├── team_b/
│   ├── ready             # Team B ready status
│   ├── code_attempt      # Code submission attempts
│   └── status            # Team B periodic updates
├── stage1/
│   └── code_generated    # Generated code broadcast
├── stage2/
│   └── target_weight     # Target weight for challenge
├── game_completed        # Victory notification
└── game_failed           # Failure notification
```

### Topic Subscriptions

**Team A subscribes to:**
```python
mqtt_client.subscribe(b"escape_room/game_control")
mqtt_client.subscribe(b"escape_room/team_b/ready")
mqtt_client.subscribe(b"escape_room/team_b/code_attempt")
mqtt_client.subscribe(b"escape_room/stage2/target_weight")
```

**Team B subscribes to:**
```python
mqtt_client.subscribe(b"escape_room/game_control")
mqtt_client.subscribe(b"escape_room/team_a/ready")
mqtt_client.subscribe(b"escape_room/stage1/code_generated")
mqtt_client.subscribe(b"escape_room/stage2/target_weight")
mqtt_client.subscribe(b"escape_room/game_failed")
mqtt_client.subscribe(b"escape_room/game_completed")
```

## Message Formats

All MQTT messages use JSON encoding for structured data exchange:

### 1. Ready Status Message
```json
{
    "team": "A",
    "ready": true,
    "timestamp": 1703123456.789
}
```
**Topic:** `escape_room/team_a/ready` or `escape_room/team_b/ready`

### 2. Game Control Messages
```json
{
    "action": "start_game",
    "team": "A",
    "game_start_time": 1703123456789,
    "timestamp": 1703123456789
}
```
**Topic:** `escape_room/game_control`
**Actions:** `start_game`, `reset_game`, `teams_ready`

### 3. Stage 1 Code Generation
```json
{
    "code": 456,
    "timestamp": 1703123456789
}
```
**Topic:** `escape_room/stage1/code_generated`

### 4. Code Attempt Message
```json
{
    "code": 123,
    "attempt": 1,
    "attempts_remaining": 4,
    "timestamp": 1703123456.789
}
```
**Topic:** `escape_room/team_b/code_attempt`

### 5. Stage 2 Target Weight
```json
{
    "stage": 2,
    "target_weight": 5000,
    "timestamp": 1703123456.789
}
```
**Topic:** `escape_room/stage2/target_weight`

### 6. Game Completion
```json
{
    "game_completed": true,
    "total_time_ms": 180000,
    "formatted_time": "3:00",
    "timestamp": 1703123456789
}
```
**Topic:** `escape_room/game_completed`

### 7. Periodic Status Updates
```json
{
    "team": "A",
    "game_state": "stage1",
    "ready": true,
    "team_b_ready": true,
    "current_code": 456,
    "target_weight": null,
    "current_weight": 0,
    "stage1_completed": false,
    "stage2_completed": false,
    "timestamp": 1703123456.789
}
```
**Topic:** `escape_room/team_a/status` (published every 2 seconds)

## Communication Flow

### Game Initialization Sequence

```mermaid
sequenceDiagram
    participant A as Team A ESP32
    participant M as MQTT Broker
    participant B as Team B ESP32
    participant N as Node-RED
    
    Note over A,B: Initialization Phase
    A->>M: Connect to broker
    B->>M: Connect to broker
    N->>M: Connect to broker
    
    A->>M: Subscribe to topics
    B->>M: Subscribe to topics
    
    Note over A,B: Ready Phase
    A->>M: publish("team_a/ready", {ready: true})
    M->>B: notify(team_a/ready)
    M->>N: notify(team_a/ready)
    
    B->>M: publish("team_b/ready", {ready: true})
    M->>A: notify(team_b/ready)
    M->>N: notify(team_b/ready)
    
    Note over A,B: Both teams ready
    A->>M: publish("game_control", {action: "teams_ready"})
    M->>B: notify(game_control)
    M->>N: notify(game_control)
    
    Note over A,B: 3-second countdown
    A->>M: publish("game_control", {action: "start_game"})
    M->>B: notify(game_control)
```

### Stage 1 Communication Flow

```mermaid
sequenceDiagram
    participant A as Team A ESP32
    participant M as MQTT Broker
    participant B as Team B ESP32
    
    Note over A: Generate code: 456
    A->>M: publish("stage1/code_generated", {code: 456})
    M->>B: notify(stage1/code_generated)
    
    Note over B: Team B attempts code
    B->>M: publish("team_b/code_attempt", {code: 123, attempts_remaining: 4})
    M->>A: notify(team_b/code_attempt)
    Note over A: Wrong code
    
    B->>M: publish("team_b/code_attempt", {code: 456, attempts_remaining: 3})
    M->>A: notify(team_b/code_attempt)
    Note over A: Correct code!
    
    A->>M: publish("stage2/target_weight", {target_weight: 5000})
    M->>B: notify(stage2/target_weight)
```

### Stage 2 Communication Flow

```mermaid
sequenceDiagram
    participant A as Team A ESP32
    participant M as MQTT Broker
    participant B as Team B ESP32
    
    Note over B: Display target: 5kg
    Note over A: Monitor pressure sensor
    
    loop Every 2 seconds
        A->>M: publish("team_a/status", {current_weight: 3200})
        M->>B: notify(team_a/status)
    end
    
    Note over A: Weight = 5000g ± 500g for 1 second
    A->>M: publish("game_completed", {total_time_ms: 180000})
    M->>B: notify(game_completed)
```

## Real-time Synchronization

### State Synchronization Strategy

1. **Event-Driven Updates**
   - Critical events (ready, code attempts, completion) sent immediately
   - State changes trigger instant MQTT publications
   - All devices receive updates within milliseconds

2. **Periodic Status Broadcasting**
   ```python
   def publish_status(self):
       if current_time - self.last_status_update > 2:  # Every 2 seconds
           status = {
               "team": "A",
               "game_state": self.game_state,
               "ready": self.ready,
               # ... other status fields
           }
           self.mqtt_client.publish(b"escape_room/team_a/status", json.dumps(status))
   ```

3. **Message Processing Loop**
   ```python
   def run(self):
       while True:
           # Check for incoming MQTT messages
           if self.mqtt_client:
               self.mqtt_client.check_msg()
           
           # Process game logic
           self.update_game_state()
           
           # 20Hz update rate
           time.sleep_ms(50)
   ```

### Callback Message Handling

```python
def mqtt_callback(self, topic, msg):
    try:
        topic = topic.decode()
        message = msg.decode()
        data = json.loads(message)
        
        if topic == "escape_room/game_control":
            if data.get("action") == "start_game":
                self.start_game()
            elif data.get("action") == "reset_game":
                self.reset_game()
                
        elif topic == "escape_room/team_b/code_attempt":
            self.handle_code_attempt(data)
            
    except Exception as e:
        log(f"MQTT callback error: {e}", "ERROR")
```

## Error Handling

### Connection Resilience

1. **Initial Connection**
   ```python
   def connect_mqtt(self):
       try:
           self.mqtt_client = MQTTClient(CLIENT_ID, BROKER, PORT)
           self.mqtt_client.connect()
           return True
       except Exception as e:
           log(f"MQTT connection failed: {e}", "ERROR")
           self.mqtt_client = None
           return False
   ```

2. **Graceful Degradation**
   - System continues locally if MQTT fails
   - Test mode activates for single-device testing
   - Critical game functions remain operational

3. **Message Validation**
   ```python
   def mqtt_callback(self, topic, msg):
       try:
           # Safe JSON parsing
           data = json.loads(msg.decode())
           
           # Validate expected fields
           if "action" not in data:
               log("Missing action field in message", "WARNING")
               return
               
       except json.JSONDecodeError:
           log("Invalid JSON in MQTT message", "ERROR")
       except Exception as e:
           log(f"Unexpected error: {e}", "ERROR")
   ```

### Network Failure Handling

1. **Publish Retry Logic**
   ```python
   def safe_publish(self, topic, message):
       if not self.mqtt_client:
           return False
           
       try:
           self.mqtt_client.publish(topic, message)
           return True
       except Exception as e:
           log(f"Publish failed: {e}", "ERROR")
           return False
   ```

2. **Subscription Recovery**
   - Store subscription list
   - Resubscribe on reconnection
   - Queue critical messages during outage

## Performance Considerations

### Bandwidth Optimization

1. **Message Size**
   - Compact JSON format
   - Minimal field names
   - Integer timestamps instead of strings
   - Average message size: 50-150 bytes

2. **Publishing Frequency**
   - Critical events: Immediate
   - Status updates: Every 2 seconds
   - Sensor readings: On significant change only

### Latency Management

1. **QoS Levels**
   - QoS 0 (At most once) for status updates
   - QoS 1 (At least once) for critical events
   - No QoS 2 to minimize overhead

2. **Message Processing**
   ```python
   # Non-blocking message check
   self.mqtt_client.check_msg()  # Returns immediately
   
   # Efficient callback processing
   def mqtt_callback(self, topic, msg):
       # Minimal processing in callback
       # Queue complex operations for main loop
   ```

### Resource Usage

1. **Memory Management**
   - Single MQTT client instance
   - Reusable message buffers
   - Limited message history

2. **CPU Optimization**
   - 20Hz main loop (50ms sleep)
   - Event-driven architecture
   - Minimal polling operations

### Scalability Features

1. **Multi-Game Support**
   - Unique client IDs per device
   - Topic namespacing for game instances
   - Broker handles multiple game sessions

2. **Load Distribution**
   - Status updates staggered
   - Event-driven reduces polling
   - Efficient topic filtering

This MQTT architecture provides reliable, low-latency communication essential for the real-time collaborative nature of the escape room game, while maintaining efficiency and scalability for multiple simultaneous game sessions.