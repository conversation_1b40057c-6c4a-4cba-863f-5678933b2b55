<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🔧 Escape Room Admin Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      html,
      body {
        height: 100%;
        overflow: auto;
        font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI",
          sans-serif;
      }

      body {
        background: linear-gradient(
          135deg,
          #0f0f23 0%,
          #1a1a2e 25%,
          #16213e 50%,
          #0f3460 75%,
          #003d82 100%
        );
        background-attachment: fixed;
        color: #ffffff;
        display: flex;
        flex-direction: column;
      }

      .header {
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(20px);
        border-bottom: 2px solid rgba(0, 255, 136, 0.5);
        padding: 12px 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        flex-shrink: 0;
      }

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 1400px;
        margin: 0 auto;
      }

      .admin-title {
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .admin-title h1 {
        font-size: 1.8rem;
        font-weight: 700;
        background: linear-gradient(135deg, #00ff88, #00cc6a);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 0 30px rgba(0, 255, 136, 0.3);
      }

      .admin-badge {
        background: linear-gradient(135deg, #00ff88, #00cc6a);
        color: #000;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .connection-status {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
      }

      .status-connected {
        background: rgba(0, 255, 136, 0.2);
        color: #00ff88;
        border: 1px solid rgba(0, 255, 136, 0.5);
      }

      .status-disconnected {
        background: rgba(255, 107, 107, 0.2);
        color: #ff6b6b;
        border: 1px solid rgba(255, 107, 107, 0.5);
        animation: pulse-red 2s infinite;
      }

      @keyframes pulse-red {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.6;
        }
      }

      .main-container {
        flex: 1;
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto auto 1fr;
        gap: 16px;
        padding: 16px;
        min-height: 0;
      }

      .status-card {
        grid-column: 1 / -1;
        background: rgba(255, 255, 255, 0.08);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      }

      .status-header {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        margin-bottom: 16px;
      }

      .status-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        background: #00ff88;
        color: #000;
      }

      .game-status {
        font-size: 1.5rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 2px;
        color: #00ff88;
        text-shadow: 0 0 20px #00ff88;
      }

      .status-description {
        text-align: center;
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.95rem;
        line-height: 1.4;
        margin-bottom: 16px;
      }

      .team-indicators {
        display: flex;
        justify-content: center;
        gap: 30px;
        margin-top: 12px;
      }

      .team-indicator {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 8px 16px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .team-led {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #666;
        box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
        animation: pulse 2s infinite;
      }

      .team-led.waiting {
        background: #ffaa00;
        box-shadow: 0 0 15px #ffaa00;
      }
      .team-led.ready {
        background: #ffff00;
        box-shadow: 0 0 15px #ffff00;
      }
      .team-led.stage1 {
        background: #00aaff;
        box-shadow: 0 0 15px #00aaff;
      }
      .team-led.stage2 {
        background: #aa00ff;
        box-shadow: 0 0 15px #aa00ff;
      }
      .team-led.completed {
        background: #00ff00;
        box-shadow: 0 0 15px #00ff00;
      }
      .team-led.failed {
        background: #ff0000;
        box-shadow: 0 0 15px #ff0000;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.6;
        }
      }

      .card {
        background: rgba(255, 255, 255, 0.08);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        display: flex;
        flex-direction: column;
        transition: all 0.3s ease;
        overflow: hidden;
        min-height: 400px;
      }

      .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        border-color: rgba(0, 255, 136, 0.4);
      }

      .card-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .card-icon {
        font-size: 1.2rem;
      }

      .card-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #00ff88;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .control-section {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-bottom: 16px;
      }

      .control-buttons {
        display: flex;
        gap: 12px;
        justify-content: center;
      }

      .btn {
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        font-size: 0.9rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        letter-spacing: 1px;
        position: relative;
        overflow: hidden;
      }

      .btn::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
      }

      .btn:hover::before {
        left: 100%;
      }

      .btn-start {
        background: linear-gradient(135deg, #00ff88, #00cc6a);
        color: #000;
        box-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
      }

      .btn-start:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 255, 136, 0.7);
      }

      .btn-reset {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        color: #fff;
        box-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
      }

      .btn-reset:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 107, 107, 0.7);
      }

      .timer-display {
        text-align: center;
        font-size: 1.8rem;
        font-weight: 900;
        color: #00ff88;
        font-family: "JetBrains Mono", "Courier New", monospace;
        text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
        background: linear-gradient(
          135deg,
          rgba(0, 255, 136, 0.1),
          rgba(0, 204, 106, 0.1)
        );
        border: 2px solid rgba(0, 255, 136, 0.3);
        border-radius: 12px;
        padding: 16px;
        animation: glow-border 3s ease-in-out infinite;
      }

      @keyframes glow-border {
        0%,
        100% {
          border-color: rgba(0, 255, 136, 0.3);
          box-shadow: 0 0 20px rgba(0, 255, 136, 0.1);
        }
        50% {
          border-color: rgba(0, 255, 136, 0.6);
          box-shadow: 0 0 30px rgba(0, 255, 136, 0.3);
        }
      }

      .game-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-top: 16px;
      }

      .info-box {
        background: rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(0, 255, 136, 0.3);
        border-radius: 12px;
        padding: 16px;
        text-align: center;
      }

      .info-label {
        font-size: 0.85rem;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 1px;
      }

      .info-value {
        font-size: 1.5rem;
        font-weight: 900;
        color: #00ff88;
        font-family: "JetBrains Mono", monospace;
        text-shadow: 0 0 15px rgba(0, 255, 136, 0.5);
      }

      .team-status-section {
        background: rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 16px;
      }

      .team-status-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        margin-bottom: 8px;
        border-left: 3px solid #00ff88;
      }

      .team-name {
        font-weight: 600;
        font-size: 1rem;
      }

      .team-state-badge {
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .state-waiting {
        background: #ffaa00;
        color: #000;
      }
      .state-ready {
        background: #ffff00;
        color: #000;
      }
      .state-stage1 {
        background: #00aaff;
        color: #fff;
      }
      .state-stage2 {
        background: #aa00ff;
        color: #fff;
      }
      .state-completed {
        background: #00ff00;
        color: #000;
      }
      .state-failed {
        background: #ff0000;
        color: #fff;
      }

      .logs-area {
        background: rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 16px;
        height: 300px;
        width: 100%;
        font-family: "JetBrains Mono", "Courier New", monospace;
        font-size: 0.85rem;
        line-height: 1.5;
        color: #ffffff;
        box-sizing: border-box;
        overflow-y: scroll;
        overflow-x: hidden;
        scroll-behavior: smooth;
      }

      .logs-area::-webkit-scrollbar {
        width: 12px;
        background: rgba(255, 255, 255, 0.1);
      }

      .logs-area::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 6px;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .logs-area::-webkit-scrollbar-thumb {
        background: #00ff88;
        border-radius: 6px;
        min-height: 40px;
        border: 2px solid rgba(255, 255, 255, 0.2);
      }

      .logs-area::-webkit-scrollbar-thumb:hover {
        background: #00cc6a;
      }

      .logs-area::-webkit-scrollbar-corner {
        background: rgba(255, 255, 255, 0.1);
      }

      .logs-area {
        scrollbar-width: thick;
        scrollbar-color: #00ff88 rgba(255, 255, 255, 0.1);
      }

      .log-entry {
        margin-bottom: 8px;
        padding: 8px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 6px;
        border-left: 3px solid #00ff88;
        animation: slide-in 0.3s ease;
        color: #ffffff;
      }

      @keyframes slide-in {
        from {
          opacity: 0;
          transform: translateX(-20px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      .log-timestamp {
        color: #888;
        font-size: 0.75rem;
        margin-bottom: 4px;
      }

      .log-content {
        color: #fff;
      }

      .no-logs {
        text-align: center;
        color: rgba(255, 255, 255, 0.4);
        font-style: italic;
        margin-top: 40px;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .main-container {
          grid-template-columns: 1fr;
          gap: 12px;
          padding: 12px;
        }

        .header-content {
          flex-direction: column;
          gap: 8px;
        }

        .admin-title h1 {
          font-size: 1.4rem;
        }

        .card {
          padding: 16px;
        }

        .control-buttons {
          flex-direction: column;
          align-items: center;
        }

        .team-indicators {
          flex-direction: column;
          gap: 12px;
        }

        .game-info {
          grid-template-columns: 1fr;
        }

        .timer-display {
          font-size: 1.4rem;
          padding: 12px;
        }
      }
    </style>
  </head>

  <body>
    <header class="header">
      <div class="header-content">
        <div class="admin-title">
          <h1>🔧 ADMIN CONTROL</h1>
          <div class="admin-badge">Game Master</div>
        </div>
        <div class="connection-status" id="connectionStatus">
          <span>🔄</span>
          <span>Connecting...</span>
        </div>
      </div>
    </header>

    <main class="main-container">
      <div class="status-card">
        <div class="status-header">
          <div class="status-icon">🎮</div>
          <div class="game-status" id="gameStatus">SYSTEM READY</div>
        </div>
        <div class="status-description" id="statusDescription">
          Escape Room system initialized. Ready to begin the challenge when both
          teams are prepared.
        </div>
        <div class="team-indicators">
          <div class="team-indicator">
            <div class="team-led waiting" id="teamALed"></div>
            <span id="teamAStatus">Team A: Waiting</span>
          </div>
          <div class="team-indicator">
            <div class="team-led waiting" id="teamBLed"></div>
            <span id="teamBStatus">Team B: Waiting</span>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <div class="card-icon">🎮</div>
          <div class="card-title">Game Control</div>
        </div>
        <div class="control-section">
          <div class="control-buttons">
            <button class="btn btn-start" onclick="startGame()">
              🚀 Start Game
            </button>
            <button class="btn btn-reset" onclick="resetGame()">
              🔄 Reset Game
            </button>
          </div>
          <div class="timer-display" id="gameTimer">⏱️ Game Not Started</div>
        </div>
        <div class="game-info">
          <div class="info-box">
            <div class="info-label">Current Code</div>
            <div class="info-value" id="currentCode">---</div>
          </div>
          <div class="info-box">
            <div class="info-label">Target Weight</div>
            <div class="info-value" id="targetWeight">---</div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <div class="card-icon">👥</div>
          <div class="card-title">Team Status</div>
        </div>
        <div class="team-status-section">
          <div class="team-status-item">
            <div class="team-name">Team A - Code Masters</div>
            <div class="team-state-badge state-waiting" id="teamAState">
              Waiting
            </div>
          </div>
          <div class="team-status-item">
            <div class="team-name">Team B - Code Breakers</div>
            <div class="team-state-badge state-waiting" id="teamBState">
              Waiting
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <div class="card-icon">📊</div>
          <div class="card-title">Game Progress</div>
        </div>
        <div class="logs-area" id="progressLog">
          <div class="no-logs">
            🎮 Admin dashboard ready. Waiting for game to start...
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-header">
          <div class="card-icon">💬</div>
          <div class="card-title">Communication Log</div>
        </div>
        <div class="logs-area" id="communicationLog">
          <div class="no-logs">📡 No communications yet...</div>
        </div>
      </div>
    </main>

    <script>
      let ws;
      let gameStartTime = null;
      let timerInterval;

      function connectWebSocket() {
        const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
        const wsUrl = `${protocol}//${window.location.host}/ws/admin`;

        ws = new WebSocket(wsUrl);

        ws.onopen = function () {
          updateConnectionStatus(true);
          ws.send(JSON.stringify({ action: "get_status" }));
        };

        ws.onclose = function () {
          updateConnectionStatus(false);
          setTimeout(connectWebSocket, 3000);
        };

        ws.onerror = function () {
          updateConnectionStatus(false);
        };

        ws.onmessage = function (event) {
          try {
            const data = JSON.parse(event.data);
            handleWebSocketMessage(data);
          } catch (e) {
            console.error("Error parsing WebSocket message:", e);
          }
        };
      }

      function updateConnectionStatus(connected) {
        const statusEl = document.getElementById("connectionStatus");
        if (connected) {
          statusEl.innerHTML = "<span>🟢</span><span>Connected</span>";
          statusEl.className = "connection-status status-connected";
        } else {
          statusEl.innerHTML = "<span>🔴</span><span>Disconnected</span>";
          statusEl.className = "connection-status status-disconnected";
        }
      }

      function handleWebSocketMessage(data) {
        switch (data.action) {
          case "status_update":
          case "heartbeat":
            updateStatus(data);
            if (data.game_start_time) {
              gameStartTime = data.game_start_time;
              startTimer();
            }
            break;
          case "team_a_status_update":
            updateTeamAStatus(data.status);
            checkGameStartFromTeamStatus(data.status);
            break;
          case "team_b_status_update":
            updateTeamBStatus(data.status);
            checkGameStartFromTeamStatus(data.status);
            break;
          case "code_generated":
            updateCurrentCode(data.code);
            if (data.progress) updateProgress(data.progress);
            break;
          case "target_weight_set":
            updateTargetWeight(data.target_weight);
            if (data.progress) updateProgress(data.progress);
            updateGameStatus("STAGE 2 - WEIGHT CHALLENGE");
            break;
          case "game_completed":
            handleGameCompleted(data);
            updateGameStatus("GAME COMPLETED");
            break;
          case "game_failed":
            updateGameStatus("GAME FAILED");
            break;
        }
      }

      function updateStatus(data) {
        if (data.team_a_status) {
          updateTeamAStatus(data.team_a_status);
          checkGameStartFromTeamStatus(data.team_a_status);
        }
        if (data.team_b_status) {
          updateTeamBStatus(data.team_b_status);
          checkGameStartFromTeamStatus(data.team_b_status);
        }

        if (data.current_code !== undefined && data.current_code !== null) {
          updateCurrentCode(data.current_code);
        }
        if (data.target_weight !== undefined && data.target_weight !== null) {
          updateTargetWeight(data.target_weight);
        }

        if (data.communication_log !== undefined) {
          updateCommunicationLog(data.communication_log);
        }

        if (data.game_progress !== undefined) {
          updateProgress(data.game_progress);
        }
      }

      function checkGameStartFromTeamStatus(teamStatus) {
        if (teamStatus && teamStatus.game_state) {
          if (teamStatus.game_state === "waiting") {
            updateGameStatus("SYSTEM READY");
          } else if (teamStatus.game_state === "ready") {
            updateGameStatus("BOTH TEAMS READY");
          } else if (teamStatus.game_state === "stage1") {
            updateGameStatus("STAGE 1 - CODE GENERATION");
          } else if (teamStatus.game_state === "stage2") {
            updateGameStatus("STAGE 2 - WEIGHT CHALLENGE");
          } else if (teamStatus.game_state === "completed") {
            updateGameStatus("GAME COMPLETED");
          } else if (teamStatus.game_state === "failed") {
            updateGameStatus("GAME FAILED");
          }
        }
      }

      function updateGameStatus(status) {
        const gameStatusEl = document.getElementById("gameStatus");
        const statusDescEl = document.getElementById("statusDescription");

        gameStatusEl.textContent = status;

        const descriptions = {
          "SYSTEM READY":
            "Escape Room system initialized. Ready to begin the challenge when both teams are prepared.",
          "BOTH TEAMS READY":
            "Both teams are ready! Game will start automatically in a few seconds.",
          "STAGE 1 - CODE GENERATION":
            "Stage 1 in progress: Team A is generating and communicating the secret code to Team B.",
          "STAGE 2 - WEIGHT CHALLENGE":
            "Stage 2 in progress: Team B has the target weight, Team A must achieve it using the pressure sensor.",
          "GAME COMPLETED":
            "🎉 Mission accomplished! Both teams successfully escaped the room! 🎉",
          "GAME FAILED": "💀 Mission failed. Too many incorrect code attempts.",
        };

        statusDescEl.textContent =
          descriptions[status] || "Game in progress...";
      }

      function updateTeamAStatus(status) {
        const led = document.getElementById("teamALed");
        const statusText = document.getElementById("teamAStatus");
        const stateElement = document.getElementById("teamAState");

        statusText.textContent = `Team A: ${status.game_state || "Unknown"}`;
        stateElement.textContent = status.game_state || "Unknown";

        led.className = "team-led " + (status.game_state || "waiting");
        stateElement.className =
          "team-state-badge state-" + (status.game_state || "waiting");
      }

      function updateTeamBStatus(status) {
        const led = document.getElementById("teamBLed");
        const statusText = document.getElementById("teamBStatus");
        const stateElement = document.getElementById("teamBState");

        statusText.textContent = `Team B: ${status.game_state || "Unknown"}`;
        stateElement.textContent = status.game_state || "Unknown";

        led.className = "team-led " + (status.game_state || "waiting");
        stateElement.className =
          "team-state-badge state-" + (status.game_state || "waiting");
      }

      function updateCurrentCode(code) {
        document.getElementById("currentCode").textContent = code || "---";
      }

      function updateTargetWeight(weight) {
        if (weight) {
          const weightKg = Math.floor(weight / 1000);
          document.getElementById("targetWeight").textContent = `${weightKg}kg`;
        } else {
          document.getElementById("targetWeight").textContent = "--kg";
        }
      }

      function updateProgress(progress) {
        const progressLog = document.getElementById("progressLog");

        if (!progress || progress.trim() === "") {
          progressLog.innerHTML =
            '<div class="no-logs">🎮 No game progress yet...</div>';
          return;
        }

        const lines = progress.split("\n").filter((line) => line.trim());

        let html = "";
        lines.forEach((line, index) => {
          if (line.trim()) {
            const timestampMatch = line.match(/^\[([^\]]+)\]/);
            const timestamp = timestampMatch ? timestampMatch[1] : "";
            const content = timestampMatch
              ? line.substring(timestampMatch[0].length).trim()
              : line;

            html += `<div class="log-content">${content}</div></div>`;
          }
        });

        if (html) {
          progressLog.innerHTML = html;
          setTimeout(() => {
            progressLog.scrollTop = progressLog.scrollHeight;
          }, 100);
        } else {
          progressLog.innerHTML =
            '<div class="no-logs">🎮 No game progress yet...</div>';
        }
      }

      function updateCommunicationLog(communications) {
        const commLog = document.getElementById("communicationLog");

        if (!communications || communications.length === 0) {
          commLog.innerHTML =
            '<div class="no-logs">📡 No communications yet...</div>';
          return;
        }

        let html = "";
        communications.slice(-10).forEach((comm, index) => {
          const time = new Date(comm.timestamp).toLocaleTimeString();
          html += `
                    <div class="log-entry">
                        <div class="log-timestamp">[${time}] ${comm.from} → ${comm.to}</div>
                        <div class="log-content">"${comm.message}"</div>
                    </div>
                `;
        });

        commLog.innerHTML = html;
        setTimeout(() => {
          commLog.scrollTop = commLog.scrollHeight;
        }, 100);
      }

      function startGame() {
        if (ws && ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify({ action: "start_game" }));
          updateGameStatus("GAME STARTING");
        }
      }

      function resetGame() {
        if (ws && ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify({ action: "reset_game" }));
        }

        gameStartTime = null;
        stopTimer();
        document.getElementById("gameTimer").textContent =
          "⏱️ Game Not Started";
        updateGameStatus("SYSTEM READY");

        document.getElementById("currentCode").textContent = "---";
        document.getElementById("targetWeight").textContent = "---";
        document.getElementById("progressLog").innerHTML =
          '<div class="no-logs">🎮 Admin dashboard ready. Waiting for game to start...</div>';
        document.getElementById("communicationLog").innerHTML =
          '<div class="no-logs">📡 No communications yet...</div>';
      }

      function startTimer() {
        if (timerInterval) clearInterval(timerInterval);

        timerInterval = setInterval(() => {
          if (gameStartTime) {
            const teamAState = document.getElementById("teamAState");
            const teamBState = document.getElementById("teamBState");
            const gameStatus = document.getElementById("gameStatus");

            // Stop timer if game is completed or failed
            if (
              gameStatus.textContent === "GAME COMPLETED" ||
              gameStatus.textContent === "GAME FAILED" ||
              teamAState.textContent.toLowerCase() === "completed" ||
              teamBState.textContent.toLowerCase() === "completed"
            ) {
              stopTimer();
              return;
            }

            const elapsed = Date.now() - gameStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            document.getElementById(
              "gameTimer"
            ).textContent = `⏱️ ${minutes}:${seconds
              .toString()
              .padStart(2, "0")}`;
          }
        }, 1000);
      }

      function stopTimer() {
        if (timerInterval) {
          clearInterval(timerInterval);
          timerInterval = null;
        }
      }

      function handleGameCompleted(data) {
        const completion = data.completion_data;
        if (completion) {
          stopTimer();
          const minutes = Math.floor(completion.total_time / 60);
          const seconds = Math.floor(completion.total_time % 60);
          document.getElementById(
            "gameTimer"
          ).textContent = `🎉 COMPLETED IN ${minutes}:${seconds
            .toString()
            .padStart(2, "0")}!`;
          updateProgress(data.progress);

          // Update team status indicators
          const teamALed = document.getElementById("teamALed");
          const teamBLed = document.getElementById("teamBLed");
          const teamAStatus = document.getElementById("teamAStatus");
          const teamBStatus = document.getElementById("teamBStatus");
          const teamAState = document.getElementById("teamAState");
          const teamBState = document.getElementById("teamBState");

          teamALed.className = "team-led completed";
          teamBLed.className = "team-led completed";
          teamAStatus.textContent = "Team A: Completed";
          teamBStatus.textContent = "Team B: Completed";
          teamAState.textContent = "COMPLETED";
          teamBState.textContent = "COMPLETED";
          teamAState.className = "team-state-badge state-completed";
          teamBState.className = "team-state-badge state-completed";

          // Show celebration effects
          document.body.classList.add("celebration");
          confetti({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 },
          });
          setTimeout(() => document.body.classList.remove("celebration"), 3000);
        }
      }

      window.addEventListener("load", connectWebSocket);
    </script>
  </body>
</html>
