# utils/network_manager.py
# MQTT Connection Manager with automatic reconnection

import time
import json
from umqtt.simple import MQTTClient
from utils.logger import log

class NetworkManager:
    def __init__(self, client_id, broker, port=1883):
        self.client_id = client_id
        self.broker = broker
        self.port = port
        self.mqtt_client = None
        self.is_connected = False
        self.last_connection_attempt = 0
        self.connection_retry_delay = 1  # Start with 1 second
        self.max_retry_delay = 30  # Max 30 seconds between retries
        self.connection_attempts = 0
        self.callback_function = None
        self.subscribed_topics = []
        
    def set_callback(self, callback):
        """Set the callback function for incoming messages"""
        self.callback_function = callback
        
    def connect(self):
        """Connect to MQTT broker with retry logic"""
        current_time = time.time()
        
        # Don't retry too frequently
        if current_time - self.last_connection_attempt < self.connection_retry_delay:
            return False
            
        self.last_connection_attempt = current_time
        self.connection_attempts += 1
        
        try:
            log(f"Attempting MQTT connection (attempt {self.connection_attempts})...", "INFO")
            self.mqtt_client = MQTTClient(self.client_id, self.broker, self.port)
            
            if self.callback_function:
                self.mqtt_client.set_callback(self.callback_function)
                
            self.mqtt_client.connect()
            self.is_connected = True
            self.connection_retry_delay = 1  # Reset retry delay on successful connection
            self.connection_attempts = 0
            
            # Re-subscribe to all topics
            for topic in self.subscribed_topics:
                self.mqtt_client.subscribe(topic.encode())
                log(f"Re-subscribed to {topic}", "DEBUG")
                
            log("MQTT connected successfully", "INFO")
            return True
            
        except Exception as e:
            self.is_connected = False
            log(f"MQTT connection failed: {e}", "ERROR")
            
            # Exponential backoff with jitter
            self.connection_retry_delay = min(
                self.connection_retry_delay * 2, 
                self.max_retry_delay
            )
            log(f"Next retry in {self.connection_retry_delay} seconds", "DEBUG")
            return False
    
    def disconnect(self):
        """Disconnect from MQTT broker"""
        try:
            if self.mqtt_client and self.is_connected:
                self.mqtt_client.disconnect()
                log("MQTT disconnected", "INFO")
        except Exception as e:
            log(f"Error during MQTT disconnect: {e}", "ERROR")
        finally:
            self.is_connected = False
            self.mqtt_client = None
    
    def subscribe(self, topic):
        """Subscribe to a topic with automatic re-subscription on reconnect"""
        if topic not in self.subscribed_topics:
            self.subscribed_topics.append(topic)
            
        if self.is_connected and self.mqtt_client:
            try:
                self.mqtt_client.subscribe(topic.encode())
                log(f"Subscribed to {topic}", "DEBUG")
                return True
            except Exception as e:
                log(f"Subscribe failed for {topic}: {e}", "ERROR")
                self.is_connected = False
                return False
        return False
    
    def publish(self, topic, message):
        """Publish message with connection validation"""
        if not self.is_connected or not self.mqtt_client:
            log("Cannot publish - not connected to MQTT", "WARNING")
            return False
            
        try:
            if isinstance(message, dict):
                message = json.dumps(message)
            elif not isinstance(message, str):
                message = str(message)
                
            self.mqtt_client.publish(topic.encode(), message.encode())
            return True
            
        except Exception as e:
            log(f"Publish failed: {e}", "ERROR")
            self.is_connected = False
            return False
    
    def check_messages(self):
        """Check for incoming messages with connection validation"""
        if not self.is_connected or not self.mqtt_client:
            return False
            
        try:
            self.mqtt_client.check_msg()
            return True
        except Exception as e:
            log(f"Message check failed: {e}", "ERROR")
            self.is_connected = False
            return False
    
    def maintain_connection(self):
        """Call this regularly to maintain connection health"""
        if not self.is_connected:
            self.connect()
        elif self.mqtt_client:
            # Verify connection is still alive
            try:
                self.mqtt_client.ping()
            except Exception as e:
                log(f"MQTT ping failed: {e}", "ERROR")
                self.is_connected = False
    
    def get_status(self):
        """Get connection status information"""
        return {
            'connected': self.is_connected,
            'broker': self.broker,
            'port': self.port,
            'client_id': self.client_id,
            'connection_attempts': self.connection_attempts,
            'retry_delay': self.connection_retry_delay,
            'subscribed_topics': self.subscribed_topics
        }

    def reset_connection(self):
        """Reset MQTT connection and resubscribe to topics"""
        try:
            if self.is_connected and self.mqtt_client:
                # Clear all game-related topics
                topics_to_clear = [
                    b"escape_room/game_control",
                    b"escape_room/team_a/ready",
                    b"escape_room/team_a/code",
                    b"escape_room/team_b/ready",
                    b"escape_room/team_b/code_attempt",
                    b"escape_room/stage2/target_weight"
                ]
                
                # Publish empty messages to clear topics
                for topic in topics_to_clear:
                    try:
                        self.mqtt_client.publish(topic, b"")
                    except:
                        pass  # Skip if topic publish fails
                
                # Resubscribe to essential topics
                self.mqtt_client.subscribe(b"escape_room/game_control")
                self.mqtt_client.subscribe(b"escape_room/team_b/ready")
                log("Reset MQTT subscriptions and cleared topics", "INFO")
        except Exception as e:
            log(f"Error resetting MQTT connection: {e}", "ERROR") 