import time
from machine import Pin, ADC
from config.hardware_config import PRESSURE_SENSOR_CONFIG
from utils.logger import log

class PressureSensor:
    def __init__(self, adc_pin):
        """Initialize pressure sensor with GPIO3 ADC"""
        try:
            self.adc = ADC(Pin(adc_pin))
            # Set attenuation for 0-3.3V range
            self.adc.atten(ADC.ATTN_11DB)
            log(f"Pressure sensor initialized on GPIO{adc_pin}", "INFO")
        except Exception as e:
            log(f"ADC initialization error on GPIO{adc_pin}: {e}", "ERROR")
            self.adc = None
        
        # Configuration
        self.config = PRESSURE_SENSOR_CONFIG
        
        # Calibration parameters
        self.zero_offset = self.config['zero_offset']
        self.scale_factor = self.config['scale_factor']
        self.is_calibrated = False
        
        # Filtering and stability
        self.readings = []
        self.last_stable_reading = 0
        self.change_threshold = self.config['change_threshold']
        
        # Statistics
        self.total_readings = 0
        self.max_reading = 0
        self.calibration_time = 0

    def calculate_moving_average(self, readings, window_size=None):
        """Calculate moving average of readings"""
        if not readings:
            return 0
        
        if window_size is None:
            window_size = len(readings)
        
        # Use only the last window_size readings
        recent_readings = readings[-window_size:]
        return sum(recent_readings) / len(recent_readings)
    
    def read_raw(self):
        """Read raw ADC value"""
        if not self.adc:
            return 0
        try:
            return self.adc.read()
        except Exception as e:
            log(f"ADC read error: {e}", "ERROR")
            return 0
    
    def read_voltage(self):
        """Convert raw reading to voltage"""
        raw = self.read_raw()
        # ESP32 ADC: 4095 = 3.3V with ATTN_11DB
        return (raw / 4095) * 3.3
    
    def calibrate_zero(self, samples=None):
        """Simple calibration - just mark as calibrated and check sensor status"""
        if samples is None:
            samples = self.config['calibration_samples']
            
        log(f"Calibrating zero point with {samples} samples...", "INFO")
        readings = []
        
        for i in range(samples):
            voltage = self.read_voltage()
            readings.append(voltage)
            log(f"Calibration sample {i+1}/{samples}: {voltage:.3f}V", "DEBUG")
            time.sleep(0.1)  # 100ms delay
        
        # Check if sensor is working (mix of low and high readings = good)
        avg_voltage = sum(readings) / len(readings)
        min_voltage = min(readings)
        max_voltage = max(readings)
        
        self.calibration_time = time.time()
        self.is_calibrated = True
        
        log(f"Sensor status: avg={avg_voltage:.3f}V, min={min_voltage:.3f}V, max={max_voltage:.3f}V", "INFO")
        log("✅ Pressure sensor calibrated and ready!", "INFO")
        return avg_voltage
    
    def read_weight(self):
        """Read weight in grams with filtering"""
        voltage = self.read_voltage()
        
        # Handle floating/disconnected sensor (readings of 4095/3.3V)
        if voltage > 3.0:  # Sensor is floating/disconnected
            weight = 0
        else:
            # For pressure sensors: lower voltage = more pressure
            # Scale 3.3V to 0kg and 1.0V to 10kg (very easy to reach max)
            # Linear mapping: (3.3-v)/(3.3-1.0) * 10kg
            weight = max(0, min(10000, (3.3 - voltage) * (10000 / 2.3)))  # 3.3V = 0kg, 1.0V = 10kg
        
        # Apply maximum limit
        weight = min(weight, self.config['max_reading'])
        
        # Update statistics
        self.total_readings += 1
        self.max_reading = max(self.max_reading, weight)
        
        # Add to moving average filter with larger window
        self.readings.append(weight)
        window_size = 10  # Increased window size for more stability
        if len(self.readings) > window_size:
            self.readings.pop(0)
        
        # Return smoothed average instead of raw reading
        smoothed_weight = sum(self.readings) / len(self.readings)
        return int(smoothed_weight)
    
    def get_stable_reading(self):
        """Get filtered stable reading"""
        if not self.readings:
            return 0
        return int(self.calculate_moving_average(self.readings))
    
    def has_significant_change(self):
        """Check if weight has changed significantly"""
        current = self.get_stable_reading()
        changed = abs(current - self.last_stable_reading) >= self.change_threshold
        
        if changed:
            self.last_stable_reading = current
            return True, current
        return False, current
    
    def get_status(self):
        """Get sensor status information"""
        return {
            'calibrated': self.is_calibrated,
            'zero_offset': self.zero_offset,
            'scale_factor': self.scale_factor,
            'current_weight': self.get_stable_reading(),
            'current_voltage': self.read_voltage(),
            'current_raw': self.read_raw(),
            'total_readings': self.total_readings,
            'max_reading': self.max_reading,
            'calibration_time': self.calibration_time,
            'adc_available': self.adc is not None,
        }


